#!/usr/bin/env python3
"""
Test script to verify the shape mismatch fix.
"""

import numpy as np
import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import GLMSingleConfig
from benchmarks.accuracy_validation import AccuracyValidator

def test_shape_mismatch_fix():
    """Test that the shape mismatch issue is fixed."""
    print("Testing shape mismatch fix...")
    
    # Initialize validator
    config = GLMSingleConfig(device='cpu')  # Use CPU for testing
    validator = AccuracyValidator(config, tolerance_abs=1e-5, tolerance_rel=1e-4)
    
    try:
        # Test edge cases that previously failed
        print("Running edge case validation...")
        edge_validation = validator.validate_edge_cases()
        
        print(f"Edge case validation completed successfully!")
        print(f"Number of tests: {len(edge_validation)}")
        
        for result in edge_validation:
            status = "✓ PASS" if result.passed else "✗ FAIL"
            print(f"  {result.operation_name}: {status}")
            if not result.passed:
                print(f"    Max abs error: {result.max_abs_error:.2e}")
                print(f"    Max rel error: {result.max_rel_error:.2e}")
        
        return True
        
    except Exception as e:
        print(f"Error during validation: {e}")
        return False

def test_calc_cod_shapes():
    """Test calc_cod with different input shapes."""
    print("\nTesting calc_cod with various shapes...")
    
    config = GLMSingleConfig(device='cpu')
    validator = AccuracyValidator(config)
    
    # Test cases with matching shapes
    test_cases = [
        ((100, 50), (100, 50)),  # Matching shapes
        ((200, 100), (200, 100)),  # Larger matching shapes
        ((50,), (50,)),  # 1D arrays
    ]
    
    for x_shape, y_shape in test_cases:
        print(f"  Testing shapes: x{x_shape}, y{y_shape}")
        
        # Generate test data
        np.random.seed(42)
        x = np.random.randn(*x_shape).astype(np.float32)
        y = np.random.randn(*y_shape).astype(np.float32)
        
        try:
            # Test PyTorch implementation
            torch_result = validator.backend.to_numpy(
                validator.stats.calc_cod(x, y, want_safe=1)
            )
            
            # Test NumPy implementation
            numpy_result = validator._numpy_calc_cod_safe(x, y)
            
            print(f"    ✓ Success - shapes compatible")
            
        except Exception as e:
            print(f"    ✗ Error: {e}")
    
    # Test case with mismatched shapes (should raise error)
    print(f"  Testing mismatched shapes (should raise error)...")
    try:
        x = np.random.randn(100, 50).astype(np.float32)
        y = np.random.randn(100, 30).astype(np.float32)  # Different second dimension
        
        torch_result = validator.stats.calc_cod(x, y, want_safe=1)
        print(f"    ✗ Error: Should have raised ValueError for mismatched shapes")
        
    except ValueError as e:
        print(f"    ✓ Correctly raised ValueError: {e}")
    except Exception as e:
        print(f"    ✗ Unexpected error: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("GLMsingle PyTorch - Shape Mismatch Fix Test")
    print("=" * 60)
    
    # Test the fix
    success1 = test_shape_mismatch_fix()
    test_calc_cod_shapes()
    
    print("\n" + "=" * 60)
    if success1:
        print("✓ All tests passed! The shape mismatch issue has been fixed.")
    else:
        print("✗ Some tests failed. Please check the error messages above.")
    print("=" * 60)
