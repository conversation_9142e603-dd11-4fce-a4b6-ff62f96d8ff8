{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Example 2: GLM estimation in a block design\n", "\n", "### Natural Scenes Dataset localizer experiment, subj01, runs 01-04\n", "\n", "---------------------\n", "\n", "##### GLMsingle is new tool that provides efficient, scalable, and accurate single-trial fMRI response estimates.\n", "\n", "The purpose of this Example 2 notebook is to guide the user through basic calls to GLMsingle, using a representative, small-scale test dataset (in this case, 4 runs from an fMRI localizer session containing a block design, which was part of the Natural Scenes Dataset).\n", "\n", "The goal is to examine the effect of GLMsingle on the reliability of fMRI response estimates to the different conditions used in the localizer set (e.g. faces, bodies, objects, scenes, words). By default, the tool implements a set of optimizations that improve upon generic GLM approaches by: (1) identifying an optimal hemodynamic response function (HRF) at each voxel, (2) deriving a set of useful GLM nuisance regressors via \"GLMdenoise\" and picking an optimal number to include in the final GLM, and (3) applying a custom amount of ridge regularization at each voxel using an efficient technique called \"fracridge\". The output of GLMsingle are GLM betas reflecting the estimated percent signal change in each voxel in response to each experimental stimulus or condition being modeled.\n", "\n", "Beyond directly improving the reliability of neural responses to repeated conditions, these optimized techniques for signal estimation can have a range of desirable downstream effects such as: improving cross-subject representational similarity within and between datasets; improving the single-image decodability of evoked neural patterns via MVPA; and, decreasing the correlation in spatial patterns observed at neighboring timepoints in analysis of fMRI GLM outputs. See our video presentation at V-VSS 2020 for a summary of these phenomena as observed in recent massive-scale fMRI datasets (the Natural Scenes Dataset and BOLD5000): https://www.youtube.com/watch?v=yb3Nn7Han8o\n", "\n", "**Example 2 contains a full walkthrough of the process of loading an example dataset and design matrix, estimating neural responses using GLMsingle, estimating the reliability of responses at each voxel, and comparing those achieved via GLMsingle to those achieved using a baseline GLM.** After loading and visualizing formatted fMRI time-series and their corresponding design matrices, we will describe the default behavior of GLMsingle and show how to modify hyperparameters if the user desires. Throughout the notebook we will highlight important metrics and outputs using figures, print statements, and comments.\n", "\n", "Users encountering bugs, unexpected outputs, or other issues regarding GLMsingle shouldn't hesitate to raise an issue on GitHub: https://github.com/kendrickkay/GLMsingle/issues\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Import function libraries"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": []}, "outputs": [], "source": ["import numpy as np\n", "import scipy\n", "import scipy.io as sio\n", "import matplotlib.pyplot as plt\n", "\n", "import os\n", "from os.path import join, exists, split\n", "import time\n", "import urllib.request\n", "import warnings\n", "from tqdm import tqdm\n", "from pprint import pprint\n", "warnings.filterwarnings('ignore')\n", "import matplotlib\n", "%matplotlib inline\n", "\n", "from glmsingle.glmsingle import GLM_single\n", "\n", "# note: the fracridge repository is also necessary to run this code\n", "# for example, you could do:\n", "#      git clone https://github.com/nrdg/fracridge.git"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Set paths and download the example dataset"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["directory to save example dataset:\n", "\t/home/<USER>/GLMsingle/examples/data\n", "\n", "directory to save example2 outputs:\n", "\t/home/<USER>/GLMsingle/examples/example2outputs\n", "\n"]}], "source": ["# get path to the directory to which GLMsingle was installed\n", "homedir = split(os.getcwd())[0]\n", "\n", "# create directory for saving data\n", "datadir = join(homedir,'examples','data')\n", "os.makedirs(datadir,exist_ok=True)\n", "\n", "# create directory for saving outputs from example 1\n", "outputdir = join(homedir,'examples','example2outputs')\n", "\n", "print(f'directory to save example dataset:\\n\\t{datadir}\\n')\n", "print(f'directory to save example2 outputs:\\n\\t{outputdir}\\n')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading example dataset and saving to:\n", "/home/<USER>/GLMsingle/examples/data/nsdflocexampledataset.mat\n"]}], "source": ["# download example dataset from GLMsingle OSF repository\n", "# data comes from subject1, floc session from NSD dataset.\n", "# https://www.biorxiv.org/content/10.1101/2021.02.22.432340v1.full.pdf\n", "\n", "datafn = join(datadir,'nsdflocexampledataset.mat')\n", "\n", "# to save time, we'll skip the download if the example dataset already exists on disk\n", "if not exists(datafn):\n", "    \n", "    print(f'Downloading example dataset and saving to:\\n{datafn}')\n", "    \n", "    dataurl = 'https://osf.io/g42tm/download'\n", "    \n", "    # download the .mat file to the specified directory\n", "    urllib.request.urlretrieve(dataurl, datafn)\n", "    \n", "# load struct containing example dataset\n", "X = sio.loadmat(datafn)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Organize BOLD data, design matrices, metadata"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# variables that will contain bold time-series and design matrices from each run\n", "data = []\n", "design = []\n", "\n", "nruns = len(X['data'][0])\n", "\n", "# iterate through each run of data\n", "for r in range(nruns):\n", "    \n", "    # index into struct, append each run's timeseries data to list\n", "    data.append(X['data'][0,r])\n", "    \n", "    # convert each run design matrix from sparse array to full numpy array, append\n", "    design.append(scipy.sparse.csr_matrix.toarray(X['design'][0,r]))\n", "    \n", "# get shape of data volume (XYZ) for convenience\n", "xyz = data[0].shape[:3]\n", "xyzt = data[0].shape\n", "\n", "# get total number of blocks - this will be the dimensionality of output betas from GLMsingle\n", "nblocks = int(np.sum(np.concatenate(design)))\n", "\n", "# get metadata about stimulus duration and TR\n", "stimdur = X['stimdur'][0][0]\n", "tr = X['tr'][0][0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualize sample data and design matrix"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"tags": []}, "outputs": [], "source": ["# data -> consists of several runs of 4D volume files (x,y,z,t)  where\n", "# (t)ime is the 4th dimention.\n", "\n", "# design -> each run has a corresponding design matrix where each column\n", "# describes a single condition (conditions are repeated across runs). each\n", "# design matrix is binary with 1 specfing the time (TR) when the stimulus\n", "# is presented on the screen.\n", "\n", "# in each run of the NSD floc experiment there were images from 10\n", "# categories presented in blocks of duration 4 sec each. each category in\n", "# the localizer set was presented for 6 blocks per run, and each block\n", "# contained 8 stimuli shown for 0.5 sec each. the individual blocks are\n", "# coded as single events in the design matrices used for the present GLM\n", "# example. as such, there are 10 predictor columns/conditions per run, with\n", "# the onset TRs for each of the 6 presentations indicated in the design\n", "# matrix. notice that the condition order is pseudo-randomized. stimulus\n", "# details are described here: https://github.com/VPNL/fLoc\n", "\n", "# X['visual'] -> maskfile defining different regions of primary visual\n", "# cortex, where (x,y,z) = integers 1 through 7 defines sets of voxels\n", "# belonging to different anatomical subsets (e.g. idx 1 corresponds to V1).\n", "# in this example, we will plot reliability values from voxels in V1.\n", "\n", "# X['floc'] -> maskfile containing manually-defined face-selective cortical\n", "# ROIs, where (x,y,z) = integers 1 through 3 defines sets of voxels\n", "# belonging to distinct ROIs (e.g. idx 1 corresponds to OFA, idx 2 to\n", "# FFA-1). in this example we will plot reliability values from voxels in\n", "# FFA.\n", "\n", "# get maskfiles for visual ROIs within which we will compare reliability\n", "V1_roi = X['visual'].item()[0] == 1   # for V1\n", "FFA_roi = X['floc'].item()[0] == 2    # for FFA-1"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# plot example slices from runs 1 and 2\n", "plt.figure(figsize=(20,6))\n", "plt.subplot(121)\n", "plt.imshow(data[0][:,:,20,0])\n", "plt.title('example slice from run 1',fontsize=16)\n", "plt.subplot(122)\n", "plt.imshow(data[1][:,:,20,0])\n", "plt.title('example slice from run 2',fontsize=16)\n", "\n", "# plot example design matrix from run 1\n", "plt.figure(figsize=(10,10))\n", "plt.imshow(design[0],aspect='auto',interpolation='none')\n", "plt.title('example design matrix from run 1',fontsize=16)\n", "plt.xlabel('conditions',fontsize=16)\n", "plt.ylabel('time (TR)',fontsize=16);"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data has 4 runs\n", "\n", "There are 240 total blocks in runs 1-4\n", "\n", "Shape of data from each run is: (52, 81, 42, 234)\n", "\n", "XYZ dimensionality is: (52, 81, 42) (one slice only)\n", "\n", "N = 234 TRs per run\n", "\n", "Numeric precision of data is: <class 'numpy.float32'>\n", "\n", "There are 484 voxels in the included FFA ROI\n", "\n", "There are 594 voxels in the included V1 ROI\n"]}], "source": ["# print some relevant metadata\n", "print(f'Data has {len(data)} runs\\n')\n", "print(f'There are {nblocks} total blocks in runs 1-4\\n')\n", "print(f'Shape of data from each run is: {data[0].shape}\\n')\n", "print(f'XYZ dimensionality is: {data[0].shape[:3]} (one slice only)\\n')\n", "print(f'N = {data[0].shape[3]} TRs per run\\n')\n", "print(f'Numeric precision of data is: {type(data[0][0,0,0,0])}\\n')\n", "print(f'There are {np.sum(FFA_roi)} voxels in the included FFA ROI\\n')\n", "print(f'There are {np.sum(V1_roi)} voxels in the included V1 ROI')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Run GLMsingle with default parameters to estimate betas for each localizer block"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# outputs and figures will be stored in a folder (you can specify its name\n", "# as the 5th output to GLMsingle). model estimates can be also\n", "# saved to the 'results' variable which is the only output of\n", "# GLMsingle.\n", "\n", "# optional parameters below can be assigned to a structure, i.e., opt =\n", "# dict('wantlibrary':1, 'wantglmdenoise':1); options are the 6th input to\n", "# GLMsingle.\n", "\n", "# there are many options that can be specified; here, we comment on the\n", "# main options that one might want to modify/set. defaults for the options\n", "# are indicated below.\n", "\n", "# wantlibrary = 1 -> fit HRF to each voxel \n", "# wantglmdenoise = 1 -> use GLMdenoise \n", "# wantfracridge = 1 -> use ridge regression to improve beta estimates \n", "# chunklen = 50000 -> is the number of voxels that we will\n", "#    process at the same time. for setups with lower memory, you may need to \n", "#    decrease this number.\n", "\n", "# wantmemoryoutputs is a logical vector [A B C D] indicating which of the\n", "#     four model types to return in the output <results>. the user must be\n", "#     careful with this, as large datasets can require a lot of RAM. if you\n", "#     do not request the various model types, they will be cleared from\n", "#     memory (but still potentially saved to disk). default: [0 0 0 1]\n", "#     which means return only the final type-D model.\n", "\n", "# wantfileoutputs is a logical vector [A B C D] indicating which of the\n", "#     four model types to save to disk (assuming that they are computed). A\n", "#     = 0/1 for saving the results of the ONOFF model, B = 0/1 for saving\n", "#     the results of the FITHRF model, C = 0/1 for saving the results of the\n", "#     FITHRF_GLMdenoise model, D = 0/1 for saving the results of the\n", "#     FITHRF_GLMdenoise_RR model. default: [1 1 1 1] which means save all\n", "#     computed results to disk.\n", "\n", "# numpcstotry (optional) is a non-negative integer indicating the maximum\n", "#     number of GLMdenoise PCs to enter into the model. default: 10.\n", "\n", "# fracs (optional) is a vector of fractions that are greater than 0\n", "#     and less than or equal to 1. we automatically sort in descending\n", "#     order and ensure the fractions are unique. these fractions indicate\n", "#     the regularization levels to evaluate using fractional ridge\n", "#     regression (fracridge) and cross-validation. default:\n", "#     fliplr(.05:.05:1). a special case is when <fracs> is specified as a\n", "#     single scalar value. in this case, cross-validation is NOT performed\n", "#     for the type-D model, and we instead blindly use the supplied\n", "#     fractional value for the type-D model."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'R2thresh': 0,\n", " 'brainR2': [],\n", " 'brainexclude': <PERSON><PERSON><PERSON>,\n", " 'brainthresh': [99.0, 0.1],\n", " 'chunklen': 50000,\n", " 'extra_regressors': <PERSON><PERSON><PERSON>,\n", " 'firdelay': 30,\n", " 'firpct': 99,\n", " 'fracs': array([1.  , 0.95, 0.9 , 0.85, 0.8 , 0.75, 0.7 , 0.65, 0.6 , 0.55, 0.5 ,\n", "       0.45, 0.4 , 0.35, 0.3 , 0.25, 0.2 , 0.15, 0.1 , 0.05]),\n", " 'hrffitmask': 1,\n", " 'hrfmodel': 'optimise',\n", " 'hrfthresh': 0.5,\n", " 'lambda': 0,\n", " 'n_boots': 100,\n", " 'n_jobs': 1,\n", " 'n_pcs': 10,\n", " 'numforhrf': 50,\n", " 'pcR2cutoff': [],\n", " 'pcR2cutoffmask': 1,\n", " 'pcstop': 1.05,\n", " 'seed': 1698109377.9396455,\n", " 'suppressoutput': 0,\n", " 'wantautoscale': 1,\n", " 'wantfileoutputs': [1, 1, 1, 1],\n", " 'wantfracridge': 1,\n", " 'wantglmdenoise': 1,\n", " 'wanthdf5': 0,\n", " 'wantlibrary': 1,\n", " 'wantlss': 0,\n", " 'wantmemoryoutputs': [1, 1, 1, 1],\n", " 'wantparametric': 0,\n", " 'wantpercentbold': 1}\n"]}], "source": ["# create a directory for saving GLMsingle outputs\n", "outputdir_glmsingle = join(homedir,'examples','example2outputs','GLMsingle')\n", "\n", "opt = dict()\n", "\n", "# set important fields for completeness (but these would be enabled by default)\n", "opt['wantlibrary'] = 1\n", "opt['wantglmdenoise'] = 1\n", "opt['wantfracridge'] = 1\n", "\n", "# for the purpose of this example we will keep the relevant outputs in memory\n", "# and also save them to the disk\n", "opt['wantfileoutputs'] = [1,1,1,1]\n", "opt['wantmemoryoutputs'] = [1,1,1,1]\n", "\n", "# running python GLMsingle involves creating a GLM_single object\n", "# and then running the procedure using the .fit() routine\n", "glmsingle_obj = GLM_single(opt)\n", "\n", "# visualize all the hyperparameters\n", "pprint(glmsingle_obj.params)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["running GLMsingle...\n", "*** DIAGNOSTICS ***:\n", "There are 4 runs.\n", "The number of conditions in this experiment is 10.\n", "The stimulus duration corresponding to each trial is 4.00 seconds.\n", "The TR (time between successive data points) is 1.33 seconds.\n", "The number of trials in each run is: [60, 60, 60, 60].\n", "The number of trials for each condition is: [24, 24, 24, 24, 24, 24, 24, 24, 24, 24].\n", "For each condition, the number of runs in which it appears: [4, 4, 4, 4, 4, 4, 4, 4, 4, 4].\n", "For each run, how much ending buffer do we have in seconds? [10.666666666666666, 10.666666666666666, 10.666666666666666, 10.666666666666666].\n", "*** Saving design-related results to /home/<USER>/GLMsingle/examples/example2outputs/GLMsingle/DESIGNINFO.npy. ***\n", "*** FITTING DIAGNOSTIC RUN-WISE FIR MODEL ***\n", "*** Saving FIR results to /home/<USER>/GLMsingle/examples/example2outputs/GLMsingle/RUNWISEFIR.npy. ***\n", "\n", "*** FITTING TYPE-A MODEL (ONOFF) ***\n", "\n", "fitting model...\n", "done.\n", "\n", "preparing output...\n", "done.\n", "\n", "computing model fits...\n", "done.\n", "\n", "computing R^2...\n", "done.\n", "\n", "computing SNR...\n", "done.\n", "\n", "\n", "*** Saving results to /home/<USER>/GLMsingle/examples/example2outputs/GLMsingle/TYPEA_ONOFF.npy. ***\n", "\n", "*** Setting brain R2 threshold to 1.4431736085837115 ***\n", "\n", "*** FITTING TYPE-B MODEL (FITHRF) ***\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["chunks: 100%|█████████████████████████████████████| 4/4 [02:50<00:00, 42.74s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Saving results to /home/<USER>/GLMsingle/examples/example2outputs/GLMsingle/TYPEB_FITHRF.npy. ***\n", "\n", "*** DETERMINING GLMDENOISE REGRESSORS ***\n", "\n", "*** CROSS-VALIDATING DIFFERENT NUMBERS OF REGRESSORS ***\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["chunks: 100%|█████████████████████████████████████| 4/4 [02:37<00:00, 39.30s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** FITTING TYPE-C MODEL (GLMDENOISE) ***\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["chunks: 100%|█████████████████████████████████████| 4/4 [00:43<00:00, 10.95s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Saving results to /home/<USER>/GLMsingle/examples/example2outputs/GLMsingle/TYPEC_FITHRF_GLMDENOISE.npy. ***\n", "\n", "*** FITTING TYPE-D MODEL (GLMDENOISE_RR) ***\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["chunks: 100%|████████████████████████████████████| 4/4 [10:00<00:00, 150.12s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Saving results to /home/<USER>/GLMsingle/examples/example2outputs/GLMsingle/TYPED_FITHRF_GLMDENOISE_RR.npy. ***\n", "\n", "*** All model types done ***\n", "\n", "*** return model types in results ***\n", "\n", "\telapsed time:  00:16:29\n"]}], "source": ["# this example saves output files to the folder  \"example2outputs/GLMsingle\"\n", "# if these outputs don't already exist, we will perform the time-consuming call to GLMsingle;\n", "# otherwise, we will just load from disk.\n", "\n", "start_time = time.time()\n", "\n", "if not exists(outputdir_glmsingle):\n", "\n", "    print(f'running GLMsingle...')\n", "    \n", "    # run GLMsingle\n", "    results_glmsingle = glmsingle_obj.fit(\n", "       design,\n", "       data,\n", "       stimdur,\n", "       tr,\n", "       outputdir=outputdir_glmsingle)\n", "    \n", "    # we assign outputs of GLMsingle to the \"results_glmsingle\" variable.\n", "    # note that results_glmsingle['typea'] contains GLM estimates from an ONOFF model,\n", "    # where all images are treated as the same condition. these estimates\n", "    # could be potentially used to find cortical areas that respond to\n", "    # visual stimuli. we want to compare beta weights between conditions\n", "    # therefore we are not going to include the ONOFF betas in any analyses of \n", "    # voxel reliability\n", "    \n", "else:\n", "    print(f'loading existing GLMsingle outputs from directory:\\n\\t{outputdir_glmsingle}')\n", "    \n", "    # load existing file outputs if they exist\n", "    results_glmsingle = dict()\n", "    results_glmsingle['typea'] = np.load(join(outputdir_glmsingle,'TYPEA_ONOFF.npy'),allow_pickle=True).item()\n", "    results_glmsingle['typeb'] = np.load(join(outputdir_glmsingle,'TYPEB_FITHRF.npy'),allow_pickle=True).item()\n", "    results_glmsingle['typec'] = np.load(join(outputdir_glmsingle,'TYPEC_FITHRF_GLMDENOISE.npy'),allow_pickle=True).item()\n", "    results_glmsingle['typed'] = np.load(join(outputdir_glmsingle,'TYPED_FITHRF_GLMDENOISE_RR.npy'),allow_pickle=True).item()\n", "\n", "elapsed_time = time.time() - start_time\n", "\n", "print(\n", "    '\\telapsed time: ',\n", "    f'{time.strftime(\"%H:%M:%S\", time.gmtime(elapsed_time))}'\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Summary of important outputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# the outputs of GLMsingle are formally documented in its\n", "# header. here, we highlight a few of the more important outputs:\n", "\n", "# R2 -> is model accuracy expressed in terms of R^2 (percentage).\n", "\n", "# betasmd -> is the full set of single-trial beta weights (X x Y x Z x\n", "# TRIALS). beta weights are arranged in chronological order.\n", "\n", "# HRFindex -> is the 1-index of the best fit HRF. HRFs can be recovered\n", "# with getcanonicalHRFlibrary(stimdur,tr)\n", "\n", "# FRACvalue -> is the fractional ridge regression regularization level\n", "# chosen for each voxel. values closer to 1 mean less regularization."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot a slice of brain showing GLMsingle outputs"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# we are going to plot several outputs from the FIT_HRF_GLMdenoise_RR GLM,\n", "# which contains the full set of GLMsingle optimizations.\n", "\n", "# we will plot betas, R2, optimal HRF indices, and the voxel frac values\n", "plot_fields = ['betasmd','R2','HRFindex','FRACvalue']\n", "colormaps = ['RdBu_r','hot','jet','copper']\n", "clims = [[-5,5],[0,85],[0,20],[0,1]]\n", "\n", "meanvol = np.squeeze(np.mean(data[0].reshape(xyzt),3))\n", "brainmask = meanvol > 275\n", "\n", "plt.figure(figsize=(12,8))\n", "\n", "for i in range(len(plot_fields)):\n", "    \n", "    plt.subplot(2,2,i+1)\n", "    \n", "    if i == 0:\n", "        # when plotting betas, for simplicity just average across all image presentations\n", "        # this will yield a summary of whether voxels tend to increase or decrease their \n", "        # activity in response to the experimental stimuli (similar to outputs from \n", "        # an ONOFF GLM)\n", "        plot_data = np.nanmean(np.squeeze(results_glmsingle['typed'][plot_fields[i]]),3).astype(float)\n", "        titlestr = 'average GLM betas (localizer runs 1-4)'\n", "    \n", "    else:\n", "        # plot all other voxel-wise metrics as outputted from GLMsingle\n", "        plot_data = np.squeeze(results_glmsingle['typed'][plot_fields[i]].reshape(xyz)).astype(float)\n", "        titlestr = plot_fields[i]\n", "    \n", "    plot_data[~brainmask] = np.nan # remove values outside the brain for visualization purposes\n", "    plt.imshow(plot_data[:,5:-5,20],cmap=colormaps[i],clim=clims[i])\n", "    plt.colorbar()\n", "    plt.title(titlestr)\n", "    plt.axis(False)\n", "    plt.show()\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Run a baseline GLM to compare with GLMsingle outputs"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'R2thresh': 0,\n", " 'brainR2': [],\n", " 'brainexclude': <PERSON><PERSON><PERSON>,\n", " 'brainthresh': [99.0, 0.1],\n", " 'chunklen': 50000,\n", " 'extra_regressors': <PERSON><PERSON><PERSON>,\n", " 'firdelay': 30,\n", " 'firpct': 99,\n", " 'fracs': array([1.  , 0.95, 0.9 , 0.85, 0.8 , 0.75, 0.7 , 0.65, 0.6 , 0.55, 0.5 ,\n", "       0.45, 0.4 , 0.35, 0.3 , 0.25, 0.2 , 0.15, 0.1 , 0.05]),\n", " 'hrffitmask': 1,\n", " 'hrfmodel': 'optimise',\n", " 'hrfthresh': 0.5,\n", " 'lambda': 0,\n", " 'n_boots': 100,\n", " 'n_jobs': 1,\n", " 'n_pcs': 10,\n", " 'numforhrf': 50,\n", " 'pcR2cutoff': [],\n", " 'pcR2cutoffmask': 1,\n", " 'pcstop': 1.05,\n", " 'seed': 1698109377.9396455,\n", " 'suppressoutput': 0,\n", " 'wantautoscale': 1,\n", " 'wantfileoutputs': [1, 1, 0, 0],\n", " 'wantfra<PERSON>ridge': 0,\n", " 'wantglmdenoise': 0,\n", " 'wanthdf5': 0,\n", " 'wantlibrary': 0,\n", " 'wantlss': 0,\n", " 'wantmemoryoutputs': [1, 1, 0, 0],\n", " 'wantparametric': 0,\n", " 'wantpercentbold': 1}\n"]}], "source": ["# for comparison purposes we are going to run a standard GLM\n", "# without HRF fitting, GLMdenoise, or ridge regression regularization. we\n", "# will compute the split-half reliability at each voxel using this baseline\n", "# GLM, and then assess whether reliability improves using the output betas\n", "# from GLMsingle. \n", "\n", "# output directory for baseline GLM\n", "outputdir_baseline = join(outputdir,'GLMbaseline')\n", "\n", "# we will run this baseline GLM by changing the default settings in GLMsingle \n", "# contained within the \"opt\" structure.\n", "opt = dict() \n", "\n", "# turn off optimizations \n", "opt['wantlibrary'] = 0 # switch off HRF fitting\n", "opt['wantglmdenoise'] = 0 # switch off GLMdenoise\n", "opt['wantfracridge'] = 0 # switch off ridge regression\n", "\n", "\n", "# for the purpose of this example we will keep the relevant outputs in memory\n", "# and also save them to the disk...\n", "# the first two indices are the ON-OFF GLM and the baseline single-trial GLM. \n", "# no need to save the third (+ GLMdenoise) and fourth (+ fracridge) outputs\n", "# since they will not even be computed\n", "opt['wantmemoryoutputs'] = [1,1,0,0] \n", "opt['wantfileoutputs'] = [1,1,0,0]\n", "\n", "# running python GLMsingle involves creating a GLM_single object\n", "# and then running the procedure using the .fit() routine\n", "glmbaseline_obj = GLM_single(opt)\n", "\n", "# visualize the hyperparameters, including the modified baseline opts\n", "pprint(glmbaseline_obj.params)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["running GLMsingle...\n", "*** DIAGNOSTICS ***:\n", "There are 4 runs.\n", "The number of conditions in this experiment is 10.\n", "The stimulus duration corresponding to each trial is 4.00 seconds.\n", "The TR (time between successive data points) is 1.33 seconds.\n", "The number of trials in each run is: [60, 60, 60, 60].\n", "The number of trials for each condition is: [24, 24, 24, 24, 24, 24, 24, 24, 24, 24].\n", "For each condition, the number of runs in which it appears: [4, 4, 4, 4, 4, 4, 4, 4, 4, 4].\n", "For each run, how much ending buffer do we have in seconds? [10.666666666666666, 10.666666666666666, 10.666666666666666, 10.666666666666666].\n", "*** Saving design-related results to /home/<USER>/GLMsingle/examples/example2outputs/GLMbaseline/DESIGNINFO.npy. ***\n", "*** FITTING DIAGNOSTIC RUN-WISE FIR MODEL ***\n", "*** Saving FIR results to /home/<USER>/GLMsingle/examples/example2outputs/GLMbaseline/RUNWISEFIR.npy. ***\n", "\n", "*** FITTING TYPE-A MODEL (ONOFF) ***\n", "\n", "fitting model...\n", "done.\n", "\n", "preparing output...\n", "done.\n", "\n", "computing model fits...\n", "done.\n", "\n", "computing R^2...\n", "done.\n", "\n", "computing SNR...\n", "done.\n", "\n", "\n", "*** Saving results to /home/<USER>/GLMsingle/examples/example2outputs/GLMbaseline/TYPEA_ONOFF.npy. ***\n", "\n", "*** Setting brain R2 threshold to 1.4431736085837115 ***\n", "\n", "*** FITTING TYPE-B MODEL (FITHRF) ***\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["chunks: 100%|█████████████████████████████████████| 4/4 [00:08<00:00,  2.21s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Saving results to /home/<USER>/GLMsingle/examples/example2outputs/GLMbaseline/TYPEB_FITHRF.npy. ***\n", "\n", "*** All model types done ***\n", "\n", "*** return model types in results ***\n", "\n", "\telapsed time:  00:00:23\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["start_time = time.time()\n", "\n", "# if these outputs don't already exist, we will perform the call to\n", "# GLMsingle; otherwise, we will just load from disk.\n", "if not exists(outputdir_baseline):\n", "    \n", "    print(f'running GLMsingle...')\n", "\n", "    # run GLMsingle, fitting the baseline GLM\n", "    results_assumehrf = glmbaseline_obj.fit(\n", "       design,\n", "       data,\n", "       stimdur,\n", "       tr,\n", "       outputdir=outputdir_baseline)\n", "    \n", "else:\n", "    \n", "    print(f'loading existing GLMsingle outputs from directory:\\n\\t{outputdir_glmsingle}')\n", "    \n", "    results_assumehrf = dict()\n", "    results_assumehrf['typea'] = np.load(join(outputdir_baseline,'TYPEA_ONOFF.npy'),allow_pickle=True).item()\n", "    results_assumehrf['typeb'] = np.load(join(outputdir_baseline,'TYPEB_FITHRF.npy'),allow_pickle=True).item()\n", "    \n", "    # note that even though we are loading TYPEB_FITHRF betas, HRF fitting\n", "    # has been turned off and this struct field will thus contain the\n", "    # outputs of a GLM fit using the canonical HRF.\n", "    \n", "# get shape of output betas for later reference\n", "xyzn = (xyz[0],xyz[1],xyz[2],nblocks)\n", "    \n", "elapsed_time = time.time() - start_time\n", "print(\n", "    '\\telapsed time: ',\n", "    f'{time.strftime(\"%H:%M:%S\", time.gmtime(elapsed_time))}'\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# create dictionary containing the GLM betas from the four different models we will compare.\n", "# note that the \"assume hrf\" betas come from the \"typeb\" field of our baseline GLM\n", "# (with HRF fitting turned off), and that the \"fit hrf\" betas also come from \n", "# the \"typeb\" field of the GLM that ran with all default GLMsingle routines\n", "# enabled\n", "\n", "models = dict()\n", "models['assumehrf'] = results_assumehrf['typeb']['betasmd'].reshape(xyzn)\n", "models['fithrf'] = results_glmsingle['typeb']['betasmd']\n", "models['fithrf_glmdenoise'] = results_glmsingle['typec']['betasmd']\n", "models['fithrf_glmdenoise_rr'] = results_glmsingle['typed']['betasmd']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Organize condition indices to prepare for reliability calculations"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# to compare the results of different GLMs we are going to calculate the\n", "# voxel-wise split-half reliablity for each model. reliability values\n", "# reflect a correlation between beta weights for repeated presentations of\n", "# the same conditions. in short, we are going to check how\n", "# reliable/reproducible are the single trial responses to repeated\n", "# conditions estimated with each GLM type.\n", "\n", "# this NSD scan session has a large number of images that are just shown\n", "# once during the session, some images that are shown twice, and a few that\n", "# are shown three times. in the code below, we are attempting to locate the\n", "# indices in the beta weight GLMsingle outputs modelmd(x,y,z,trials) that\n", "# correspond to repeated images. here we only consider stimuli that have\n", "# been presented at least twice. for the purpose of the example we ignore\n", "# the 3rd repetition of the stimulus.\n", "\n", "# consolidate design matrices\n", "designALL = np.concatenate(design,axis=0)\n", "\n", "# construct a vector containing 0-indexed condition numbers in chronological order\n", "corder = []\n", "for p in range(designALL.shape[0]):\n", "    if np.any(designALL[p]):\n", "        corder.append(np.argwhere(designALL[p])[0,0])\n", "        \n", "corder = np.array(corder)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[9 7 6]\n"]}], "source": ["# let's take a look at the first few entries\n", "print(corder[:3])\n", "\n", "# note that [9 7 6] means that the first stimulus block involved\n", "# presentation of the 9th condition (zero-indexed), the second stimulus block \n", "# involved presentation of the 7th condition, and so on."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 10 repeated conditions in the experiment\n", "\n", "There are 24 instances of each repeated condition across 4 runs\n", "\n", "Betas from blocks containing the first localizer condition can be found at the following indices of GLMsingle output beta matrices:\n", "\n", "[  7  12  16  19  33  56  63  75  84  96 105 113 125 136 141 150 170 173\n", " 194 201 203 213 226 236]\n"]}], "source": ["# in order to compute split-half reliability, we have to do some indexing.\n", "# we want to find all repetitions of the same condition. for example we can\n", "# look up when during the 4 blocks image 1 was repeated. each condition should\n", "# be repeated exactly 24 times.\n", "\n", "repindices = []\n", "\n", "for p in range(designALL.shape[1]): # loop over every condition\n", "    temp = np.argwhere(corder==p)[:,0] # find indices where this condition was shown\n", "    if len(temp) >= 2:\n", "        repindices.append(temp)\n", "\n", "repindices = np.vstack(np.array(repindices)).T      \n", "\n", "print(f'There are {repindices.shape[1]} repeated conditions in the experiment\\n')\n", "print(f'There are {repindices.shape[0]} instances of each repeated condition across 4 runs\\n')\n", "print(f'Betas from blocks containing the first localizer condition can be found at the following indices of GLMsingle output beta matrices:\\n\\n{repindices[:,0]}')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# now, for each voxel we are going to correlate beta weights describing the\n", "# response to averaged even presentations of the 10 conditions with beta\n", "# weights describing the responses from the averaged odd repetitions of the\n", "# same conditions, over time. with 10 conditions in the localizer set, the\n", "# split-half correlation for each voxel will thus reflect the relationship\n", "# between two vectors with 10 beta weights each.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualize FFA and V1 ROIs, within which we will compute reliability"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["plt.figure(figsize=(22,6))\n", "c=1\n", "\n", "FFA_roi_ = np.full(FFA_roi.shape, np.nan)\n", "FFA_roi_[FFA_roi] = 1\n", "\n", "V1_roi_ = np.full(V1_roi.shape, np.nan)\n", "V1_roi_[V1_roi] = 1\n", "\n", "# show some slices containing FFA voxels overlaid against the mean volume from run 1\n", "for s in range(3,8):\n", "    plt.subplot(2,5,c)\n", "    plt.imshow(meanvol[15:,5:-5,s]/np.nanmax(meanvol[15:,5:-5,s]),aspect='auto',cmap='gray',clim=(0,1), alpha=0.7)\n", "    plt.imshow(FFA_roi_[15:,5:-5,s],aspect='auto',cmap='hot',clim=(0,2))\n", "    plt.title('FFA voxels, slice ' + str(s))\n", "    plt.box(False)\n", "    plt.axis(False)\n", "    c+=1\n", "\n", "# show some slices containing V1 voxels overlaid against the mean volume from run 1\n", "for s in range(10,15):\n", "    plt.subplot(2,5,c)\n", "    plt.imshow(meanvol[15:,5:-5,s]/np.nanmax(meanvol[15:,5:-5,s]),aspect='auto',cmap='gray',clim=(0,1), alpha=0.7)\n", "    plt.imshow(V1_roi_[15:,5:-5,s],aspect='auto',cmap='Blues',clim=(0,1.25))\n", "    plt.title('V1 voxels, slice ' + str(s))\n", "    plt.box(False)\n", "    plt.axis(False)\n", "    c+=1\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Compute median split-half reliability within the ROIs for each GLM version"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["computing reliability for beta version: assumehrf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████| 52/52 [00:00<00:00, 403.65it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["computing reliability for beta version: fithrf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████| 52/52 [00:00<00:00, 394.99it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["computing reliability for beta version: fithrf_glmdenoise\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████| 52/52 [00:00<00:00, 402.83it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["computing reliability for beta version: fithrf_glmdenoise_rr\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████| 52/52 [00:00<00:00, 400.10it/s]\n"]}], "source": ["# To calculate the split-half reliability we are going to average the odd\n", "# and even beta weights extracted from the same condition and calculate the\n", "# correlation coefficent between these values. We do this for each voxel\n", "# inside the primary visual cortex and face-selective cortical ROIs.\n", "\n", "vox_reliabilities = [] # output variable for reliability values\n", "\n", "modelnames = list(models.keys())\n", "n_cond = repindices.shape[1]\n", "\n", "# for each beta version...\n", "for m in range(len(modelnames)):\n", "    \n", "    print(f'computing reliability for beta version: {modelnames[m]}')\n", "    time.sleep(1)\n", "    \n", "    # organize the betas by (X,Y,Z,repeats,conditions) using the repindices variable\n", "    betas = models[modelnames[m]][:,:,:,repindices]\n", "    x,y,z = betas.shape[:3] \n", "    \n", "    # create output volume for voxel reliability scores\n", "    rels = np.full((x,y,z),np.nan)\n", "    \n", "    # loop through voxels in the 3D volume...\n", "    for xx in tqdm(range(x)):\n", "        for yy in range(y):\n", "            for zz in range(z):\n", "                \n", "                # process only if within ROIs of interest\n", "                if FFA_roi[xx,yy,zz] or V1_roi[xx,yy,zz]: \n", "                    \n", "                    # for this voxel, get beta matrix of (repeats,conditions)\n", "                    vox_data = betas[xx,yy,zz]\n", "                    \n", "                    # average odd and even betas after shuffling\n", "                    even_data = np.nanmean(vox_data[::2],axis=0)\n", "                    odd_data = np.nanmean(vox_data[1::2],axis=0)\n", "\n", "                    # reliability at a given voxel is pearson correlation between the \n", "                    # odd- and even-presentation beta vectors\n", "                    rels[xx,yy,zz] = np.corrcoef(even_data,odd_data)[1,0]\n", "          \n", "    vox_reliabilities.append(rels)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Assess change in reliability yielded by GLMsingle"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# to perform a comparison between all GLM versions available in our visual\n", "# ROIs, we will simply loop through the beta versions, isolate the voxels\n", "# of interest, compute the median split-half correlation value within each\n", "# ROI, and plot using a bar graph.\n", "\n", "comparison = []\n", "for vr in vox_reliabilities:\n", "    comparison.append([np.nanmedian(vr[FFA_roi]), \n", "                       np.nanmedian(vr[V1_roi])])\n", "comparison = np.vstack(comparison)\n", "\n", "plt.figure(figsize=(18,6))\n", "plt.subplot(121)\n", "plt.bar(np.arange(len(comparison[:,0]))-0.125,comparison[:,0],width=0.2)\n", "plt.bar(np.arange(len(comparison[:,1]))+0.125,comparison[:,1],width=0.2)\n", "plt.ylim([0,0.7])\n", "\n", "plt.legend(['FFA','V1'])\n", "plt.title('Median voxel split-half reliability of GLM models')\n", "plt.xticks(np.arange(4),np.array(['ASSUMEHRF', 'FITHRF', 'FITHRF\\nGLMDENOISE', 'FITHRF\\nGLMDENOISE\\nRR']));\n", "\n", "# we can also look at how distributions of FFA/V1 voxel reliabilities change \n", "# between the baseline GLM and the final output of GLMsingle (fithrf+GLMdenoise+RR)\n", "plt.subplot(122)\n", "plt.hist(vox_reliabilities[0].reshape(-1),25,alpha=0.6,color='tomato');\n", "plt.hist(vox_reliabilities[3].reshape(-1),25,alpha=0.6,color='limegreen');\n", "plt.xlabel('reliability (r)')\n", "plt.ylabel('# voxels')\n", "plt.legend(['baseline\\n(ASSUMEHRF)', 'G<PERSON>single\\n(FITHRF_GLMDENOISE_RR)'])\n", "plt.title('Change in distribution of FFA and V1 voxel reliabilities\\ndue to GLMsingle');\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# notice that there is systematic increase in reliability moving from the\n", "# first to the second to the third to the final fourth version of the GLM\n", "# results, within both the early visual and face-selective ROIs. these\n", "# increases reflect, respectively, the addition of HRF fitting, the\n", "# derivation and use of data-driven nuisance regressors, and the use of\n", "# ridge regression as a way to regularize the instability of closely spaced\n", "# experimental trials. depending on one's experimental goals, it is\n", "# possible with setting of option flags to activate a subset of these\n", "# analysis features.\n", "\n", "# also, keep in mind that in the above figure, we are simply showing the\n", "# median as a metric of the central tendency (you may want to peruse\n", "# individual voxels in scatter plots, for example).\n", "\n", "# note also that median reliability is consistently higher in the FFA ROI\n", "# than in the V1 ROI in this analysis of localizer data. importantly, this\n", "# does not imply that V1 data are inherently noisy or unreliable across the\n", "# NSD dataset (e.g. in the NSD core experiment). here we are analyzing GLM\n", "# outputs from the localizer dataset, which was designed to identify neural\n", "# ROIs whose voxel tuning profiles vary meaningfully across a small,\n", "# curated set of high-level stimulus categories. as such, we would expect\n", "# the FFA voxels to have relatively more well-defined tuning curves, with\n", "# more substantial variability in GLM betas between the localizer\n", "# categories, than the V1 voxels, whose tuning is determined by lower-level\n", "# image features that do not necessarily vary in systematic ways between\n", "# the localizer categories. for these reasons, one might expect that\n", "# split-half correlations would be somewhat lower in the V1 voxels using\n", "# our particular metric of reliability (odd-even correlation) in this\n", "# dataset. different metrics of reliability may generate different patterns\n", "# of results when comparing these two ROIs, and we would also expect\n", "# results to vary depending on the stimulus set.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot voxel-wise change in reliability due to GLMsingle as an overlay on the brain"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for a final visualization, we can look at the relationship between voxels'\n", "# locations in anatomical space and the degree to which GLMsingle confers\n", "# benefit over a baseline GLM\n", "\n", "# comparison is the final output (FIT_HRF_GLMDENOISE_RR) vs. the baseline\n", "# GLM (ASSUME_HRF)\n", "vox_improvement = vox_reliabilities[3] - vox_reliabilities[0]\n", "c=1\n", "plt.figure(figsize=(55/1.5,18/1.5))\n", "for s in range(3,17):\n", "    plot_data = vox_improvement[15:,5:-5,s]\n", "    plt.subplot(3,5,c)\n", "    plt.imshow(meanvol[15:,5:-5,s]/np.nanmax(meanvol[15:,5:-5,s]),\n", "               aspect='auto',cmap='gray',clim=(0,1), alpha=0.5)\n", "    plt.imshow(plot_data,aspect='auto',cmap='RdBu_r',clim=(-0.3,0.3))\n", "    plt.title(f'median \\u0394r in slice: +{np.round(np.nanmedian(plot_data),3)}',fontsize=18)\n", "    plt.colorbar()\n", "    plt.box(False)\n", "    plt.axis(False)\n", "    c+=1\n", "    \n", "# in these plots, higher positive values (shades of red) reflect a greater benefit from \n", "# applying GLMsingle. "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}