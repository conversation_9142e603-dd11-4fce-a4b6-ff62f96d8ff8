{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this script we recommend running example2 first to create all the\n", "necessary outpouts from GLMsingle that are going to be reused here.\n", "\n", "This script shows how to find single-trial beta weights in the output of\n", "the GLMsingle. We will show how to average them to create one response\n", "to each condition rather then a response to each trial. This will produce\n", "one beta weight for each condition. Additionaly we will show how to \n", "calculate a t-statistic for each condition and a contrast between two \n", "example conditions in the fLoc experiment (number vs. face) using single \n", "trial betas.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Import function libraries"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import scipy\n", "import scipy.io as sio\n", "import matplotlib.pyplot as plt\n", "\n", "import os\n", "from os.path import join, exists, split\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from glmsingle.glmsingle import GLM_single"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Set paths and load the example2 design matrices"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# get path to the directory to which GLMsingle was installed\n", "homedir = split(os.getcwd())[0]\n", "\n", "datadir = join(homedir,'examples','data')\n", "datafn = join(datadir,'nsdflocexampledataset.mat')\n", "X = sio.loadmat(datafn)\n", "\n", "nruns = len(X['data'][0])\n", "design = []\n", "data  = []\n", "# iterate through each run of data\n", "for r in range(nruns):\n", "    \n", "    # convert each run design matrix from sparse array to full numpy array, append\n", "    design.append(scipy.sparse.csr_matrix.toarray(X['design'][0,r]))\n", "    data.append(X['data'][0,r])\n", "\n", "\n", "xyz = data[0].shape[:3]\n", "xyzt = data[0].shape\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# This NSD fLOC scan session has 6 repetitions of each condition per run.\n", "# In the code below, we are attempting to locate the indices in the beta\n", "# weight GLMsingle outputs modelmd(x,y,z,trials) that correspond to\n", "# repeated conditions.\n", "\n", "designALL = np.concatenate(design,axis=0)\n", "\n", "# construct a vector containing 0-indexed condition numbers in chronological order\n", "corder = []\n", "for p in range(designALL.shape[0]):\n", "    if np.any(designALL[p]):\n", "        corder.append(np.argwhere(designALL[p])[0,0])\n", "        \n", "corder = np.array(corder)\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'There are 24 repetition of 10 unique conditions'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# We will now find indices for each condition. First lets find all unique\n", "# condition in the corder list\n", "\n", "repindices = []\n", "\n", "for p in range(designALL.shape[1]): # loop over every condition\n", "    temp = np.argwhere(corder==p)[:,0] # find indices where this condition was shown\n", "    if len(temp) >= 3:\n", "        repindices.append(temp)\n", "\n", "repindices = np.vstack(np.array(repindices)).T      \n", "\n", "\"There are %i repetition of %i unique conditions\" % (np.shape(repindices)[0], np.shape(repindices)[1])"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["# Knowing the indicies of each condition we can now load an example output\n", "# of GLMsingle and create 1 beta weight for each condition. In the example\n", "# 1 there are 10 unique conditions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load GLMsingle results from example2 "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["results_glmsingle = dict()\n", "outputdir_glmsingle = join(homedir,'examples','example2outputs','GLMsingle')\n", "results_glmsingle['typed'] = np.load(join(outputdir_glmsingle,'TYPED_FITHRF_GLMDENOISE_RR.npy'),allow_pickle=True).item()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["betas = results_glmsingle['typed']['betasmd']\n", "condition_list = np.unique(corder)\n", "betas_average = np.zeros([np.shape(betas)[0],np.shape(betas)[1],np.shape(betas)[2],len(condition_list)])\n", "\n", "for k in condition_list:\n", "    betas_average[:,:,:,k] = np.mean(betas[:,:,:,np.where(corder==k)[0]],3)\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GLMsingle output had 240 betas before averaging \n", "\n", "GLMsingle output has 10 betas after averaging \n", "\n"]}], "source": ["print('GLMsingle output had %i betas before averaging \\n' % np.shape(betas)[3])\n", "print('GLMsingle output has %i betas after averaging \\n' % np.shape(betas_average)[3])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Calculate t-statistic for each condition\n", "# We calculate t-stat as the mean over the standard deviation across all\n", "# repetitions of the same condition. \n", "\n", "t_stat = np.zeros([np.shape(betas)[0],np.shape(betas)[1],np.shape(betas)[2],len(condition_list)])\n", "for k in condition_list:\n", "    t_stat[:,:,:,k] = np.mean(betas[:,:,:,np.where(corder==k)[0]],3) / np.std(betas[:,:,:,np.where(corder==k)[0]],3)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(52, 81, 42, 24)\n"]}], "source": ["# Calculate a contrast between condition 2 (number) and condition 5 (adult face)\n", "cond1 = betas[:,:,:,np.where(corder==1)[0]]\n", "cond2 = betas[:,:,:,np.where(corder==4)[0]]\n", "np.shape(cond2)\n", "ttest = scipy.stats.ttest_ind(cond1,cond2,3)\n", "tval = ttest[0];\n", "pval = ttest[1]\n", "print(np.shape(cond2))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(52, 81, 42, 10)\n"]}, {"data": {"text/plain": ["(-0.5, 70.5, 36.5, -0.5)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 3666.67x1200 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib\n", "%matplotlib inline\n", "\n", "meanvol = np.squeeze(np.mean(data[0].reshape(xyzt),3))\n", "brainmask = meanvol > 275\n", "plt.figure(figsize=(55/1.5,18/1.5))\n", "c=1\n", "s = 16 # pick a slice\n", "plot_data = t_stat\n", "print(np.shape(plot_data))\n", "plot_data[~brainmask] = np.nan # remove values outside the brain for visualization purposes\n", "plot_data = plot_data[15:,5:-5,s,0]\n", "plt.subplot(3,5,c)\n", "plt.imshow(meanvol[15:,5:-5,s]/np.nanmax(meanvol[15:,5:-5,s]),\n", "            aspect='auto',cmap='gray',clim=(0,1), alpha=0.5)\n", "plt.imshow(plot_data,aspect='auto',cmap='RdBu_r',clim=(-2,2))\n", "plt.title('T-stat for the first condition',fontsize=18)\n", "plt.colorbar()\n", "plt.box(False)\n", "plt.axis(False)\n", "# The t-statistic (t_stat) shows high values in the visual cortex"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["(-0.5, 70.5, 36.5, -0.5)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 3666.67x1200 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(55/1.5,18/1.5))\n", "\n", "s = 21 # pick a slice\n", "plot_data = tval\n", "plot_data[~brainmask] = np.nan # remove values outside the brain for visualization purposes\n", "plot_data[~(pval<0.05)] = np.nan # remove values below p<0.05\n", "\n", "\n", "plot_data = plot_data[15:,5:-5,s]\n", "plt.subplot(3,5,1)\n", "plt.imshow(meanvol[15:,5:-5,s]/np.nanmax(meanvol[15:,5:-5,s]),\n", "            aspect='auto',cmap='gray',clim=(0,1), alpha=0.5)\n", "plt.imshow(plot_data,aspect='auto',cmap='RdBu_r',clim=(-4,4))\n", "plt.title('T-test (cond2 vs. cond5)',fontsize=18)\n", "plt.colorbar()\n", "plt.box(False)\n", "plt.axis(False)\n", "# t-test between two conditions shows positive voxels in more ventral and\n", "# lateral portions of the brain."]}], "metadata": {"celltoolbar": "Raw Cell Format", "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 4}