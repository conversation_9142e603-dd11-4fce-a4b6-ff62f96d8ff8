# GLMsingle PyTorch Requirements
# Core dependencies for GPU-accelerated GLMsingle implementation

# PyTorch ecosystem (with CUDA support)
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0

# Core scientific computing
numpy>=1.21.0
scipy>=1.7.0

# Machine learning and preprocessing
scikit-learn>=1.0.0

# Data handling and I/O
h5py>=3.1.0

# Visualization
matplotlib>=3.3.0

# Progress bars and utilities
tqdm>=4.62.0

# System monitoring (for benchmarking)
psutil>=5.8.0

# Optional: Jupyter notebook support
jupyter>=1.0.0
ipykernel>=6.0.0

# Optional: Enhanced plotting
seaborn>=0.11.0

# Development and testing (optional)
pytest>=6.0.0
pytest-cov>=2.12.0

# Note: For CUDA support, install PyTorch with CUDA:
# pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
