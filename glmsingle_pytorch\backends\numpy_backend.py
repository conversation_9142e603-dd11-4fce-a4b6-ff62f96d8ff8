"""
NumPy backend for GLMsingle compatibility.

This module provides a NumPy-based backend that maintains compatibility
with the original GLMsingle implementation while providing a consistent
interface with the PyTorch backend.

This backend serves as:
- A fallback when PyTorch/CUDA is not available
- A reference implementation for accuracy validation
- A baseline for performance comparisons
- A compatibility layer for existing NumPy-based workflows

Key Features:
- Identical API to PyTorch backend
- Full compatibility with original GLMsingle operations
- Optimized NumPy implementations where possible
- Comprehensive error handling and validation
- Memory-efficient processing for large datasets
"""

import numpy as np
import warnings
from typing import Union, List, Tuple, Optional, Dict, Any
from contextlib import contextmanager
import time

from ..config import GLMSingleConfig


class NumpyBackend:
    """
    NumPy backend for GLMsingle computations.
    
    This backend provides NumPy-based implementations of all core GLMsingle
    operations, maintaining full compatibility with the original implementation
    while providing a consistent interface with the PyTorch backend.
    
    The backend is designed to serve as a fallback when GPU acceleration
    is not available and as a reference implementation for validation.
    
    Attributes:
        config (GLMSingleConfig): Configuration object
        dtype (np.dtype): Default array data type
    """
    
    def __init__(self, config: Optional[GLMSingleConfig] = None):
        """
        Initialize NumPy backend.
        
        Args:
            config: Configuration object (device settings ignored for NumPy)
        """
        self.config = config or GLMSingleConfig()
        self.dtype = np.float32  # Use float32 for consistency with PyTorch
        
        # Performance tracking
        self._timing_context = {}
        self._memory_stats = {}
        
        print(f"NumpyBackend initialized:")
        print(f"  Data type: {self.dtype}")
        print(f"  Chunk size: {self.config.chunk_size_cpu}")
        print(f"  Memory efficient: {self.config.memory_efficient}")
    
    @contextmanager
    def timing_context(self, operation_name: str):
        """Context manager for timing operations."""
        if self.config.timing_enabled:
            start_time = time.time()
            
            try:
                yield
            finally:
                elapsed = time.time() - start_time
                self._timing_context[operation_name] = elapsed
    
    def to_array(self, data: Union[np.ndarray, List], 
                 dtype: Optional[np.dtype] = None) -> np.ndarray:
        """
        Convert input data to NumPy array.
        
        Args:
            data: Input data (numpy array or list)
            dtype: Target data type (defaults to backend dtype)
            
        Returns:
            np.ndarray: NumPy array
        """
        if dtype is None:
            dtype = self.dtype
        
        if isinstance(data, np.ndarray):
            return data.astype(dtype)
        else:
            return np.array(data, dtype=dtype)
    
    def get_optimal_chunk_size(self, data_shape: Tuple[int, ...], 
                              operation_memory_factor: float = 2.0) -> int:
        """
        Calculate optimal chunk size based on available memory.
        
        Args:
            data_shape: Shape of data to be processed
            operation_memory_factor: Memory multiplication factor for operation
            
        Returns:
            int: Optimal chunk size
        """
        # For NumPy backend, use CPU chunk size
        return self.config.chunk_size_cpu
    
    def clear_cache(self):
        """Clear memory cache (no-op for NumPy)."""
        pass
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get current memory statistics."""
        import psutil
        
        stats = {
            'device': 'cpu',
            'memory_usage_gb': psutil.virtual_memory().used / 1024**3,
            'memory_available_gb': psutil.virtual_memory().available / 1024**3
        }
        
        if hasattr(self, '_memory_stats'):
            stats.update(self._memory_stats)
        
        return stats
    
    def get_timing_stats(self) -> Dict[str, float]:
        """Get timing statistics for operations."""
        return self._timing_context.copy()
    
    def reset_stats(self):
        """Reset timing and memory statistics."""
        self._timing_context.clear()
        self._memory_stats.clear()
    
    # Core mathematical operations
    def olsmatrix(self, X: np.ndarray, mode: int = 0, 
                  regularization: float = 1e-8) -> np.ndarray:
        """
        Compute OLS matrix for regression.
        
        This is the reference NumPy implementation that matches the
        original GLMsingle olsmatrix function.
        
        Args:
            X: Design matrix (samples x parameters)
            mode: Operation mode (0=normal, 1=use inv instead of solve)
            regularization: Ridge regularization parameter for stability
            
        Returns:
            np.ndarray: OLS matrix (parameters x samples)
        """
        with self.timing_context("olsmatrix"):
            from sklearn.preprocessing import normalize
            
            # Handle degenerate regressors (all zeros)
            good_regressors = ~np.all(X == 0, axis=0)
            
            if not np.any(good_regressors):
                # All regressors are degenerate
                return np.zeros((X.shape[1], X.shape[0]), dtype=self.dtype)
            
            # Initialize result matrix
            f = np.zeros((X.shape[1], X.shape[0]), dtype=self.dtype)
            
            # Extract good regressors
            X_good = X[:, good_regressors]
            
            if mode == 0:
                # Normal operation with unit-length normalization
                X_normalized = normalize(X_good, axis=0)
                norms = np.linalg.norm(X_good, axis=0)
                
                # Compute (X'X + λI)^(-1) X' with regularization for stability
                XtX = X_normalized.T @ X_normalized
                XtX += regularization * np.eye(XtX.shape[0])  # Add regularization
                
                try:
                    XtX_inv = np.linalg.inv(XtX)
                except np.linalg.LinAlgError:
                    # Fallback to pseudo-inverse
                    XtX_inv = np.linalg.pinv(XtX)
                
                # Apply normalization correction
                temp = np.diag(1.0 / norms) @ XtX_inv @ X_normalized.T
                
            else:
                # Mode 1: Direct inverse without normalization
                XtX = X_good.T @ X_good
                XtX += regularization * np.eye(XtX.shape[0])  # Add regularization
                
                try:
                    XtX_inv = np.linalg.inv(XtX)
                except np.linalg.LinAlgError:
                    # Fallback to pseudo-inverse
                    XtX_inv = np.linalg.pinv(XtX)
                
                temp = XtX_inv @ X_good.T
            
            # Assign results to good regressors
            f[good_regressors, :] = temp
            
            return f
    
    def ridge_regression(self, X: np.ndarray, y: np.ndarray,
                        alpha: Union[float, np.ndarray]) -> np.ndarray:
        """
        Perform ridge regression.
        
        Args:
            X: Design matrix (samples x features)
            y: Target values (samples x targets)
            alpha: Regularization parameter(s)
            
        Returns:
            np.ndarray: Ridge regression coefficients
        """
        with self.timing_context("ridge_regression"):
            X = self.to_array(X)
            y = self.to_array(y)
            
            if isinstance(alpha, (int, float)):
                alpha = np.array([alpha], dtype=self.dtype)
            else:
                alpha = self.to_array(alpha)
            
            # Compute X'X and X'y
            XtX = X.T @ X
            Xty = X.T @ y
            
            if alpha.size == 1:
                # Single alpha value
                XtX_reg = XtX + alpha[0] * np.eye(XtX.shape[0])
                try:
                    coeffs = np.linalg.solve(XtX_reg, Xty)
                except np.linalg.LinAlgError:
                    coeffs = np.linalg.lstsq(XtX_reg, Xty, rcond=None)[0]
            else:
                # Multiple alpha values
                n_features = XtX.shape[0]
                n_targets = Xty.shape[1] if Xty.ndim > 1 else 1
                n_alphas = alpha.size
                
                coeffs = np.zeros((n_alphas, n_features, n_targets), dtype=self.dtype)
                
                eye = np.eye(n_features, dtype=self.dtype)
                
                for i, a in enumerate(alpha):
                    XtX_reg = XtX + a * eye
                    try:
                        coeffs[i] = np.linalg.solve(XtX_reg, Xty)
                    except np.linalg.LinAlgError:
                        coeffs[i] = np.linalg.lstsq(XtX_reg, Xty, rcond=None)[0]
            
            return coeffs
    
    def convolve_design(self, X: np.ndarray, hrf: np.ndarray,
                       mode: str = 'full') -> np.ndarray:
        """
        Convolve design matrix with HRF.
        
        Args:
            X: Design matrix (time x conditions)
            hrf: Hemodynamic response function
            mode: Convolution mode ('full', 'valid', 'same')
            
        Returns:
            np.ndarray: Convolved design matrix
        """
        with self.timing_context("convolve_design"):
            X = self.to_array(X)
            hrf = self.to_array(hrf)
            
            if X.ndim == 1:
                # Single time series
                return np.convolve(X, hrf, mode=mode)
            else:
                # Multiple conditions
                n_times, n_conditions = X.shape
                
                if mode == 'same':
                    result = np.zeros_like(X)
                    for i in range(n_conditions):
                        result[:, i] = np.convolve(X[:, i], hrf, mode='same')
                elif mode == 'full':
                    result_length = n_times + len(hrf) - 1
                    result = np.zeros((result_length, n_conditions), dtype=self.dtype)
                    for i in range(n_conditions):
                        result[:, i] = np.convolve(X[:, i], hrf, mode='full')
                else:  # 'valid'
                    result_length = max(0, n_times - len(hrf) + 1)
                    result = np.zeros((result_length, n_conditions), dtype=self.dtype)
                    for i in range(n_conditions):
                        result[:, i] = np.convolve(X[:, i], hrf, mode='valid')
                
                return result
    
    def calc_cod(self, x: np.ndarray, y: np.ndarray,
                 dim: Optional[int] = None,
                 want_gain: int = 0,
                 want_mean_sub: int = 1,
                 want_safe: int = 1) -> np.ndarray:
        """
        Calculate coefficient of determination (R²).
        
        Args:
            x: Predicted values (same shape as y)
            y: Actual values (same shape as x)
            dim: Dimension along which to compute R² (auto-detected if None)
            want_gain: 0=normal, 1=allow gain, 2=non-negative gain
            want_mean_sub: Whether to subtract mean before calculation
            want_safe: Whether to handle NaN values safely
            
        Returns:
            np.ndarray: Coefficient of determination (R²) values
        """
        with self.timing_context("calc_cod"):
            x = self.to_array(x)
            y = self.to_array(y)
            
            if dim is None:
                dim = np.argmax(x.shape)
            
            # Handle gain adjustment
            if want_gain > 0:
                # Calculate optimal gain: g = (x'y) / (x'x)
                gain = np.sum(x * y, axis=dim, keepdims=True) / \
                       np.sum(x * x, axis=dim, keepdims=True)
                
                if want_gain == 2:
                    # Restrict to non-negative gains
                    gain = np.maximum(gain, 0)
                
                # Apply gain
                x = x * gain
            
            # Handle NaN propagation
            if want_safe:
                nan_mask = np.isnan(x) | np.isnan(y)
                x = x.copy()
                y = y.copy()
                x[nan_mask] = np.nan
                y[nan_mask] = np.nan
            
            # Handle mean subtraction
            if want_mean_sub:
                if want_safe:
                    y_mean = np.nanmean(y, axis=dim, keepdims=True)
                else:
                    y_mean = np.mean(y, axis=dim, keepdims=True)
                y = y - y_mean
                x = x - y_mean
            
            # Calculate R² = 100 * (1 - SS_res / SS_tot)
            if want_safe:
                ss_res = np.nansum((y - x) ** 2, axis=dim)
                ss_tot = np.nansum(y ** 2, axis=dim)
            else:
                ss_res = np.sum((y - x) ** 2, axis=dim)
                ss_tot = np.sum(y ** 2, axis=dim)
            
            # Avoid division by zero
            with np.errstate(divide='ignore', invalid='ignore'):
                r2 = 100 * (1 - ss_res / ss_tot)
                r2[ss_tot == 0] = np.nan
            
            return r2
    
    def calculate_correlation(self, x: np.ndarray, y: np.ndarray,
                            dim: Optional[int] = None) -> np.ndarray:
        """
        Calculate Pearson correlation coefficient.
        
        Args:
            x: First variable
            y: Second variable
            dim: Dimension along which to compute correlation
            
        Returns:
            np.ndarray: Correlation coefficients
        """
        with self.timing_context("calculate_correlation"):
            x = self.to_array(x)
            y = self.to_array(y)
            
            if dim is None:
                # Flatten and compute single correlation
                x_flat = x.flatten()
                y_flat = y.flatten()
                
                # Remove NaN values
                valid_mask = ~(np.isnan(x_flat) | np.isnan(y_flat))
                x_valid = x_flat[valid_mask]
                y_valid = y_flat[valid_mask]
                
                if len(x_valid) < 2:
                    return np.array(np.nan)
                
                # Calculate correlation
                correlation = np.corrcoef(x_valid, y_valid)[0, 1]
                return correlation if not np.isnan(correlation) else np.array(0.0)
            else:
                # Compute correlation along specified dimension
                x_mean = np.nanmean(x, axis=dim, keepdims=True)
                y_mean = np.nanmean(y, axis=dim, keepdims=True)
                
                x_centered = x - x_mean
                y_centered = y - y_mean
                
                numerator = np.nansum(x_centered * y_centered, axis=dim)
                x_var = np.nansum(x_centered ** 2, axis=dim)
                y_var = np.nansum(y_centered ** 2, axis=dim)
                denominator = np.sqrt(x_var * y_var)
                
                with np.errstate(divide='ignore', invalid='ignore'):
                    correlation = numerator / denominator
                    correlation[denominator == 0] = np.nan
                
                return correlation
