"""
Performance benchmarking utilities for GLMsingle PyTorch.

This module provides comprehensive performance testing and benchmarking
capabilities to validate GPU acceleration gains and compare performance
between NumPy and PyTorch implementations.

Key Features:
- Comprehensive timing analysis for all major operations
- Memory usage profiling and optimization validation
- Scalability testing across different dataset sizes
- GPU vs CPU performance comparisons
- Detailed performance reporting and visualization

Performance Metrics Tracked:
- Matrix operations (OLS, ridge regression, matrix multiplication)
- Convolution operations (HRF convolution, design matrix construction)
- Statistical computations (R², variance, correlation)
- Cross-validation and model fitting pipelines
- Memory allocation and GPU utilization
"""

import torch
import numpy as np
import time
import psutil
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass
import json
import os

from ..config import GLMSingleConfig
from ..backends.torch_backend import TorchBackend
from ..torch_ops.linear_algebra import TorchLinearAlgebra
from ..torch_ops.convolution import TorchConvolution
from ..torch_ops.statistics import TorchStatistics


@dataclass
class BenchmarkResult:
    """Container for benchmark results."""
    operation_name: str
    torch_time: float
    numpy_time: Optional[float] = None
    speedup: Optional[float] = None
    memory_used: Optional[float] = None
    device: str = 'cpu'
    data_size: Optional[Tuple] = None
    additional_metrics: Optional[Dict] = None


class PerformanceBenchmark:
    """
    Comprehensive performance benchmarking for GLMsingle PyTorch.
    
    This class provides detailed performance analysis and comparison
    between PyTorch GPU-accelerated and NumPy CPU implementations
    across all major GLMsingle operations.
    
    Features:
    - Operation-level timing analysis
    - Memory usage profiling
    - Scalability testing
    - GPU utilization monitoring
    - Comprehensive reporting
    """
    
    def __init__(self, config: Optional[GLMSingleConfig] = None):
        """
        Initialize performance benchmark.
        
        Args:
            config: Configuration for PyTorch backend
        """
        self.config = config or GLMSingleConfig()
        self.backend = TorchBackend(self.config)
        
        # Initialize operation modules
        self.linalg = TorchLinearAlgebra(self.backend)
        self.conv = TorchConvolution(self.backend)
        self.stats = TorchStatistics(self.backend)
        
        # Results storage
        self.results: List[BenchmarkResult] = []
        self.system_info = self._get_system_info()
        
        print(f"Performance benchmark initialized:")
        print(f"  Device: {self.backend.device}")
        print(f"  System: {self.system_info['cpu_info']}")
        if self.backend.device.type == 'cuda':
            print(f"  GPU: {torch.cuda.get_device_name()}")
    
    def benchmark_linear_algebra(self, sizes: List[Tuple[int, int]] = None) -> List[BenchmarkResult]:
        """
        Benchmark linear algebra operations.
        
        Args:
            sizes: List of matrix sizes to test (samples, features)
            
        Returns:
            List of benchmark results
        """
        if sizes is None:
            sizes = [(1000, 100), (5000, 500), (10000, 1000), (20000, 2000)]
        
        print("Benchmarking linear algebra operations...")
        results = []
        
        for n_samples, n_features in sizes:
            print(f"  Testing size: {n_samples} x {n_features}")
            
            # Generate test data
            X_np = np.random.randn(n_samples, n_features).astype(np.float32)
            y_np = np.random.randn(n_samples, 100).astype(np.float32)
            
            # Benchmark OLS matrix computation
            result = self._benchmark_operation(
                "OLS Matrix",
                lambda: self.linalg.olsmatrix(X_np),
                lambda: self._numpy_olsmatrix(X_np),
                data_size=(n_samples, n_features)
            )
            results.append(result)
            
            # Benchmark ridge regression
            alphas = np.logspace(-3, 1, 10)
            result = self._benchmark_operation(
                "Ridge Regression",
                lambda: self.linalg.ridge_regression(X_np, y_np, alphas),
                lambda: self._numpy_ridge_regression(X_np, y_np, alphas),
                data_size=(n_samples, n_features)
            )
            results.append(result)
            
            # Clear GPU cache
            self.backend.clear_cache()
        
        self.results.extend(results)
        return results
    
    def benchmark_convolution(self, sizes: List[Tuple[int, int]] = None) -> List[BenchmarkResult]:
        """
        Benchmark convolution operations.
        
        Args:
            sizes: List of sizes to test (timepoints, conditions)
            
        Returns:
            List of benchmark results
        """
        if sizes is None:
            sizes = [(1000, 50), (2000, 100), (5000, 200), (10000, 500)]
        
        print("Benchmarking convolution operations...")
        results = []
        
        # Generate HRF
        hrf_np = np.random.randn(30).astype(np.float32)
        
        for n_timepoints, n_conditions in sizes:
            print(f"  Testing size: {n_timepoints} x {n_conditions}")
            
            # Generate test design matrix
            X_np = np.random.randn(n_timepoints, n_conditions).astype(np.float32)
            
            # Benchmark design matrix convolution
            result = self._benchmark_operation(
                "HRF Convolution",
                lambda: self.conv.convolve_design(X_np, hrf_np),
                lambda: self._numpy_convolve_design(X_np, hrf_np),
                data_size=(n_timepoints, n_conditions)
            )
            results.append(result)
            
            # Clear GPU cache
            self.backend.clear_cache()
        
        self.results.extend(results)
        return results
    
    def benchmark_statistics(self, sizes: List[Tuple[int, int]] = None) -> List[BenchmarkResult]:
        """
        Benchmark statistical operations.
        
        Args:
            sizes: List of sizes to test (timepoints, voxels)
            
        Returns:
            List of benchmark results
        """
        if sizes is None:
            sizes = [(1000, 1000), (2000, 5000), (5000, 10000), (10000, 20000)]
        
        print("Benchmarking statistical operations...")
        results = []
        
        for n_timepoints, n_voxels in sizes:
            print(f"  Testing size: {n_timepoints} x {n_voxels}")
            
            # Generate test data
            x_np = np.random.randn(n_timepoints, n_voxels).astype(np.float32)
            y_np = np.random.randn(n_timepoints, n_voxels).astype(np.float32)
            
            # Benchmark R² calculation
            result = self._benchmark_operation(
                "R² Calculation",
                lambda: self.stats.calc_cod(x_np, y_np),
                lambda: self._numpy_calc_cod(x_np, y_np),
                data_size=(n_timepoints, n_voxels)
            )
            results.append(result)
            
            # Benchmark correlation calculation
            result = self._benchmark_operation(
                "Correlation",
                lambda: self.stats.calculate_correlation(x_np, y_np),
                lambda: self._numpy_correlation(x_np, y_np),
                data_size=(n_timepoints, n_voxels)
            )
            results.append(result)
            
            # Clear GPU cache
            self.backend.clear_cache()
        
        self.results.extend(results)
        return results
    
    def benchmark_end_to_end(self, data_sizes: List[Tuple] = None) -> List[BenchmarkResult]:
        """
        Benchmark end-to-end GLMsingle pipeline.
        
        Args:
            data_sizes: List of (n_voxels, n_timepoints, n_conditions) tuples
            
        Returns:
            List of benchmark results
        """
        if data_sizes is None:
            data_sizes = [
                (1000, 200, 20),
                (5000, 500, 50),
                (10000, 1000, 100),
                (20000, 2000, 200)
            ]
        
        print("Benchmarking end-to-end pipeline...")
        results = []
        
        for n_voxels, n_timepoints, n_conditions in data_sizes:
            print(f"  Testing dataset: {n_voxels} voxels, {n_timepoints} timepoints, {n_conditions} conditions")
            
            # Generate synthetic dataset
            design = [np.random.randn(n_timepoints, n_conditions).astype(np.float32)]
            data = [np.random.randn(n_timepoints, n_voxels).astype(np.float32)]
            
            # Benchmark PyTorch implementation
            start_time = time.time()
            
            # Simulate GLM fitting
            X = self.backend.to_tensor(design[0])
            y = self.backend.to_tensor(data[0])
            
            # OLS fitting
            ols_matrix = self.linalg.olsmatrix(X)
            betas = ols_matrix @ y
            
            # R² calculation
            predictions = X @ betas
            r2 = self.stats.calc_cod(predictions, y)
            
            torch_time = time.time() - start_time
            
            # Get memory usage
            memory_used = None
            if self.backend.device.type == 'cuda':
                memory_used = torch.cuda.max_memory_allocated() / 1024**3  # GB
            
            result = BenchmarkResult(
                operation_name="End-to-End Pipeline",
                torch_time=torch_time,
                memory_used=memory_used,
                device=str(self.backend.device),
                data_size=(n_voxels, n_timepoints, n_conditions)
            )
            
            results.append(result)
            
            # Clear GPU cache
            self.backend.clear_cache()
        
        self.results.extend(results)
        return results
    
    def _benchmark_operation(self, name: str, 
                           torch_func: Callable, 
                           numpy_func: Optional[Callable] = None,
                           data_size: Optional[Tuple] = None,
                           n_runs: int = 3) -> BenchmarkResult:
        """Benchmark a single operation."""
        # Warm up
        try:
            torch_func()
        except Exception as e:
            print(f"Warning: PyTorch operation failed: {e}")
            return BenchmarkResult(name, float('inf'), device=str(self.backend.device))
        
        # Benchmark PyTorch
        torch_times = []
        for _ in range(n_runs):
            if self.backend.device.type == 'cuda':
                torch.cuda.synchronize()
            
            start_time = time.time()
            torch_func()
            
            if self.backend.device.type == 'cuda':
                torch.cuda.synchronize()
            
            torch_times.append(time.time() - start_time)
        
        torch_time = np.mean(torch_times)
        
        # Benchmark NumPy if provided
        numpy_time = None
        speedup = None
        
        if numpy_func:
            try:
                # Warm up
                numpy_func()
                
                numpy_times = []
                for _ in range(n_runs):
                    start_time = time.time()
                    numpy_func()
                    numpy_times.append(time.time() - start_time)
                
                numpy_time = np.mean(numpy_times)
                speedup = numpy_time / torch_time if torch_time > 0 else float('inf')
            except Exception as e:
                print(f"Warning: NumPy operation failed: {e}")
        
        # Get memory usage
        memory_used = None
        if self.backend.device.type == 'cuda':
            memory_used = torch.cuda.max_memory_allocated() / 1024**3  # GB
        
        return BenchmarkResult(
            operation_name=name,
            torch_time=torch_time,
            numpy_time=numpy_time,
            speedup=speedup,
            memory_used=memory_used,
            device=str(self.backend.device),
            data_size=data_size
        )
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information."""
        info = {
            'cpu_info': f"{psutil.cpu_count()} cores",
            'memory_gb': psutil.virtual_memory().total / 1024**3,
            'python_version': torch.__version__,
            'torch_version': torch.__version__
        }
        
        if torch.cuda.is_available():
            info['gpu_name'] = torch.cuda.get_device_name()
            info['gpu_memory_gb'] = torch.cuda.get_device_properties(0).total_memory / 1024**3
        
        return info
    
    # NumPy reference implementations for comparison
    def _numpy_olsmatrix(self, X: np.ndarray) -> np.ndarray:
        """NumPy reference implementation of OLS matrix."""
        from sklearn.preprocessing import normalize
        
        good = ~np.all(X == 0, axis=0)
        f = np.zeros((X.shape[1], X.shape[0]))
        
        if np.any(good):
            X_good = X[:, good]
            X_norm = normalize(X_good, axis=0)
            norms = np.linalg.norm(X_good, axis=0)
            
            XtX = X_norm.T @ X_norm
            XtX_inv = np.linalg.pinv(XtX)
            temp = np.diag(1.0 / norms) @ XtX_inv @ X_norm.T
            f[good, :] = temp
        
        return f
    
    def _numpy_ridge_regression(self, X: np.ndarray, y: np.ndarray, 
                               alphas: np.ndarray) -> np.ndarray:
        """NumPy reference implementation of ridge regression."""
        XtX = X.T @ X
        Xty = X.T @ y
        
        results = []
        for alpha in alphas:
            XtX_reg = XtX + alpha * np.eye(XtX.shape[0])
            try:
                coeffs = np.linalg.solve(XtX_reg, Xty)
            except np.linalg.LinAlgError:
                coeffs = np.linalg.lstsq(XtX_reg, Xty, rcond=None)[0]
            results.append(coeffs)
        
        return np.array(results)
    
    def _numpy_convolve_design(self, X: np.ndarray, hrf: np.ndarray) -> np.ndarray:
        """NumPy reference implementation of design convolution."""
        n_time, n_cond = X.shape
        result = np.zeros_like(X)
        
        for i in range(n_cond):
            conv_result = np.convolve(X[:, i], hrf, mode='full')
            result[:, i] = conv_result[:n_time]
        
        return result
    
    def _numpy_calc_cod(self, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """NumPy reference implementation of R² calculation."""
        ss_res = np.sum((y - x) ** 2, axis=0)
        ss_tot = np.sum(y ** 2, axis=0)
        
        with np.errstate(divide='ignore', invalid='ignore'):
            r2 = 100 * (1 - ss_res / ss_tot)
            r2[ss_tot == 0] = np.nan
        
        return r2
    
    def _numpy_correlation(self, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """NumPy reference implementation of correlation."""
        x_centered = x - np.mean(x, axis=0)
        y_centered = y - np.mean(y, axis=0)
        
        numerator = np.sum(x_centered * y_centered, axis=0)
        x_var = np.sum(x_centered ** 2, axis=0)
        y_var = np.sum(y_centered ** 2, axis=0)
        denominator = np.sqrt(x_var * y_var)
        
        with np.errstate(divide='ignore', invalid='ignore'):
            corr = numerator / denominator
            corr[denominator == 0] = np.nan
        
        return corr
    
    def generate_report(self, save_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate comprehensive benchmark report.
        
        Args:
            save_path: Path to save report (optional)
            
        Returns:
            Dict containing full benchmark report
        """
        if not self.results:
            print("No benchmark results available. Run benchmarks first.")
            return {}
        
        # Aggregate results
        report = {
            'system_info': self.system_info,
            'device_used': str(self.backend.device),
            'total_operations': len(self.results),
            'operations': {}
        }
        
        # Group results by operation
        for result in self.results:
            op_name = result.operation_name
            if op_name not in report['operations']:
                report['operations'][op_name] = []
            
            report['operations'][op_name].append({
                'torch_time': result.torch_time,
                'numpy_time': result.numpy_time,
                'speedup': result.speedup,
                'memory_used': result.memory_used,
                'data_size': result.data_size
            })
        
        # Calculate summary statistics
        speedups = [r.speedup for r in self.results if r.speedup is not None]
        if speedups:
            report['summary'] = {
                'mean_speedup': np.mean(speedups),
                'median_speedup': np.median(speedups),
                'max_speedup': np.max(speedups),
                'min_speedup': np.min(speedups)
            }
        
        # Save report if path provided
        if save_path:
            with open(save_path, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            print(f"Benchmark report saved to {save_path}")
        
        return report
