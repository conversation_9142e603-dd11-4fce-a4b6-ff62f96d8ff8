{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this script we recommend running example2 first to create all the\n", "necessary outpouts from GLMsingle that are going to be reused here.\n", "This script shows how to save GLMsingle outputs as NIFTI files. NIFTI is \n", "neuroimaging format often used to store neuroimaging data. This script \n", "requires a nibabel package\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import nibabel as nib\n", "import numpy as np\n", "import os\n", "from os.path import join, exists, split"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# load GLMsingle results\n", "homedir = split(os.getcwd())[0]\n", "datadir = join(homedir,'examples','data')\n", "results_glmsingle = dict()\n", "outputdir_glmsingle = join(homedir,'examples','example2outputs','GLMsingle')\n", "results_glmsingle['typed'] = np.load(join(outputdir_glmsingle,'TYPED_FITHRF_GLMDENOISE_RR.npy'),allow_pickle=True).item()\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Create nifti file\n", "# Creating a new image in some file format is also easy. \n", "# At a minimum it only needs some image data and an image coordinate transformation (affine):\n", "\n", "vals2save = ['R2','betasmd'] # pick maps that you want to save as nifti\n", "savepath = os.path.join(outputdir_glmsingle,'nifti')\n", "if not os.path.exists(savepath):\n", "    os.mkdir(savepath)\n", "for v in vals2save:\n", "    img = nib.Nifti1Image(results_glmsingle['typed'][v], np.eye(4))\n", "    nib.save(img, os.path.join(savepath,'%s.nii.gz' %v))  \n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 4}