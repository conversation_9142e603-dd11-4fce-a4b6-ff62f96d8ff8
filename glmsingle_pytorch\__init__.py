"""
GLMsingle PyTorch - GPU-Accelerated Single-Trial fMRI Analysis

This package provides a PyTorch-accelerated implementation of GLMsingle,
offering significant performance improvements through GPU acceleration while
maintaining full backward compatibility with the original NumPy implementation.

Key Features:
- GPU acceleration for matrix operations and convolutions
- Automatic device management (CPU/GPU)
- Memory-optimized tensor operations
- Identical numerical results to original implementation
- Comprehensive benchmarking and validation tools

Authors: <AUTHORS>
Based on original GLMsingle by Prince, J.S<PERSON>, <PERSON><PERSON>, <PERSON>, et al.
"""

from .glmsingle_torch import GLMSingle_PyTorch
from .backends.torch_backend import TorchBackend
from .config import GLMSingleConfig

__version__ = "1.0.0"
__all__ = ["GLMSingle_PyTorch", "TorchBackend", "GLMSingleConfig"]
