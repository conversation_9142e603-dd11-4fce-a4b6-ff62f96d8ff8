{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["This example shows how to interpolate the fMRI timecourse if the stimulus\n", "duration is not equal to the duration of the TR. Imagine that there are \n", "100 TRs and each lasts for 1s, while your stimulus duration is only 0.5s\n", "The stimulus starts with the TR onset. To correctly code the stimulus \n", "onset in your design matrix you need 200 rows in your design matrix \n", "(but you only have 100 fMRI volumes). Interpolation of the timecourse is \n", "necessary to match the size of rows in your design matrix. "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["#%pip install git+https://github.com/Charestlab/pyslicetime.git \n", "import numpy as np\n", "import scipy as sc\n", "import glmsingle\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from slicetime.tseriesinterp import tseriesinterp\n", "import matplotlib\n", "%matplotlib inline\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["TRs = 100\n", "TR = 1\n", "stimdur = 0.5\n", "TRs_after_resampling = int(TR/stimdur*TRs)\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Use an example hrf and create two example fMRI time series.\n", "cond1 = np.zeros([TRs,1])\n", "cond2 = np.zeros([TRs,1])\n", "\n", "\n", "cond1[[10,50]] = 1\n", "cond2[[30,60]] = 1\n", "\n", "\n", "\n", "hrf = glmsingle.glmsingle.getcanonicalhrf(stimdu<PERSON>,TR)\n", "\n", "tcs = np.convolve(cond1[:,0],hrf)\n", "tcs = tcs[0:len(cond1)]\n", "tcs2 = np.convolve(cond2[:,0],hrf)\n", "tcs2 = tcs2[0:len(cond2)]\n", "\n", "cond1 = (cond1[:,0])\n", "cond2 = (cond2[:,0])\n", "\n", "tcs = (tcs,tcs2)\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(15, 6), dpi=80)\n", "plt.plot(tcs[0])\n", "plt.plot(tcs[1])\n", "plt.xlabel('TRs')\n", "plt.ylabel('%BOLD')\n", "plt.title('Example timecourses')\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Resample"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2, 100)\n", "(2, 200)\n"]}, {"data": {"text/plain": ["array([[ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         3.12275504e-02,  6.59638143e-02,  2.00212205e-01,\n", "         3.46899611e-01,  5.13110771e-01,  6.82014863e-01,\n", "         8.01978939e-01,  9.14545105e-01,  9.60165050e-01,\n", "         9.94758559e-01,  9.75772897e-01,  9.47219526e-01,\n", "         8.84599587e-01,  8.15412598e-01,  7.32581228e-01,\n", "         6.46921144e-01,  5.61395831e-01,  4.75900467e-01,\n", "         3.98037086e-01,  3.21986300e-01,  2.56846029e-01,\n", "         1.94467914e-01,  1.43424620e-01,  9.54330084e-02,\n", "         5.78348214e-02,  2.32061843e-02, -2.91253071e-03,\n", "        -2.64558745e-02, -4.33367685e-02, -5.80856785e-02,\n", "        -6.79667621e-02, -7.62033098e-02, -8.09172769e-02,\n", "        -8.43766264e-02, -8.55197602e-02, -8.57943133e-02,\n", "        -8.45724193e-02, -8.27603772e-02, -8.01718266e-02,\n", "        -7.72615796e-02, -7.38538123e-02, -7.02297318e-02,\n", "        -6.64547860e-02, -6.26110634e-02, -5.87933550e-02,\n", "        -5.49880714e-02, -5.13292146e-02, -4.77435712e-02,\n", "        -4.43490049e-02, -4.10543865e-02, -3.79798537e-02,\n", "        -3.50256802e-02, -3.22916460e-02, -2.96834057e-02,\n", "        -2.72883727e-02, -2.50205762e-02, -2.29456318e-02,\n", "        -2.09908246e-02, -1.92057408e-02, -1.75309770e-02,\n", "        -1.60067444e-02, -1.45845668e-02, -1.32942852e-02,\n", "        -1.20972404e-02, -1.10108483e-02, -1.00059897e-02,\n", "        -9.09120614e-03, -8.24558735e-03, -7.48233409e-03,\n", "        -6.78497324e-03, -6.15080913e-03, -5.56930895e-03,\n", "        -5.04189814e-03, -4.56143281e-03, -4.12509547e-03,\n", "        -3.72864303e-03, -3.36868099e-03, -3.04306285e-03,\n", "        -2.74628645e-03, -2.47777498e-03,  1.49589727e-02,\n", "         4.99143287e-02,  1.39129592e-01,  2.85996006e-01,\n", "         4.43394094e-01,  6.12444247e-01,  7.55304425e-01,\n", "         8.67989197e-01,  9.45221620e-01,  9.79911222e-01,\n", "         9.86527978e-01,  9.58053678e-01,  9.11923779e-01,\n", "         8.42800541e-01,  7.66683476e-01,  6.81074788e-01,\n", "         5.95600880e-01,  5.10317635e-01,  4.28764676e-01,\n", "         3.52713890e-01,  2.82049308e-01,  2.19671193e-01,\n", "         1.62815170e-01,  1.14823559e-01,  7.18261900e-02,\n", "         3.71975528e-02,  6.59993142e-03, -1.69434124e-02,\n", "        -3.73776129e-02, -5.21265229e-02, -6.46388641e-02,\n", "        -7.28754117e-02, -7.95195600e-02, -8.29789094e-02,\n", "        -8.54088297e-02, -8.56833828e-02, -8.53045576e-02,\n", "        -8.34925154e-02, -8.13476840e-02, -7.84374370e-02,\n", "        -7.53180872e-02, -7.16940067e-02, -6.80078052e-02,\n", "        -6.41640826e-02, -6.03308433e-02, -5.65255597e-02,\n", "        -5.27779594e-02, -4.91923160e-02, -4.56801638e-02,\n", "        -4.23855455e-02, -3.91734592e-02, -3.62192856e-02,\n", "        -3.33454804e-02, -3.07372401e-02, -2.82046541e-02,\n", "        -2.59368576e-02, -2.37354529e-02, -2.17806457e-02,\n", "        -1.98824130e-02, -1.82076492e-02, -1.65813616e-02,\n", "        -1.51591840e-02, -1.37779396e-02, -1.25808949e-02,\n", "        -1.14168518e-02, -1.04119932e-02, -9.43287030e-03,\n", "        -8.58725151e-03, -7.76409605e-03, -7.06673520e-03,\n", "        -6.38575869e-03, -5.80425852e-03, -5.23602554e-03,\n", "        -4.75556022e-03, -4.28527827e-03, -3.88882583e-03,\n", "        -3.50024387e-03, -3.17462573e-03, -2.85477594e-03,\n", "        -2.58626446e-03, -2.32224566e-03, -2.10315359e-03,\n", "        -1.88730067e-03, -1.70829302e-03, -1.53161490e-03,\n", "        -1.38555350e-03, -1.24115606e-03, -1.12254980e-03,\n", "        -1.00508058e-03, -9.08987516e-04, -8.13582218e-04,\n", "        -7.34511374e-04, -6.55904771e-04, -5.92153906e-04,\n", "        -5.28652633e-04, -4.77256577e-04, -4.24237061e-04,\n", "        -2.12118531e-04,  0.00000000e+00],\n", "       [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  0.00000000e+00,  0.00000000e+00,\n", "         0.00000000e+00,  2.42101233e-02,  5.89463873e-02,\n", "         1.70578385e-01,  3.17265792e-01,  4.78988733e-01,\n", "         6.47892824e-01,  7.79238299e-01,  8.91804465e-01,\n", "         9.53176462e-01,  9.87769972e-01,  9.81541255e-01,\n", "         9.52987884e-01,  8.98576756e-01,  8.29389768e-01,\n", "         7.49886295e-01,  6.64226211e-01,  5.78667622e-01,\n", "         4.93172258e-01,  4.13400881e-01,  3.37350095e-01,\n", "         2.69447669e-01,  2.07069553e-01,  1.53119895e-01,\n", "         1.05128283e-01,  6.48305057e-02,  3.02018685e-02,\n", "         1.84370035e-03, -2.16996434e-02, -4.03571907e-02,\n", "        -5.51061007e-02, -6.63028131e-02, -7.45393608e-02,\n", "        -8.02184185e-02, -8.36777679e-02, -8.54642950e-02,\n", "        -8.57388481e-02, -8.49384885e-02, -8.31264463e-02,\n", "        -8.07597553e-02, -7.78495083e-02, -7.45859498e-02,\n", "        -7.09618692e-02, -6.72312956e-02, -6.33875730e-02,\n", "        -5.95620992e-02, -5.57568155e-02, -5.20535870e-02,\n", "        -4.84679436e-02, -4.50145843e-02, -4.17199660e-02,\n", "        -3.85766565e-02, -3.56224829e-02, -3.28185632e-02,\n", "        -3.02103229e-02, -2.77465134e-02, -2.54787169e-02,\n", "        -2.33405423e-02, -2.13857352e-02, -1.95440769e-02,\n", "        -1.78693131e-02, -2.61007027e-03,  3.35483713e-02,\n", "         1.12591544e-01,  2.60475995e-01,  4.16591824e-01,\n", "         5.86500775e-01,  7.35865301e-01,  8.49277086e-01,\n", "         9.35070366e-01,  9.70361236e-01,  9.83925508e-01,\n", "         9.55953637e-01,  9.14403548e-01,  8.45697025e-01,\n", "         7.71638710e-01,  6.86375078e-01,  6.01140845e-01,\n", "         5.15971100e-01,  4.33646042e-01,  3.57863768e-01,\n", "         2.86072143e-01,  2.23913120e-01,  1.65811670e-01,\n", "         1.17999066e-01,  7.38219246e-02,  3.93393488e-02,\n", "         7.76085176e-03, -1.56638858e-02, -3.68734918e-02,\n", "        -5.15263088e-02, -6.46044979e-02, -7.27619747e-02,\n", "        -7.98131566e-02, -8.32087551e-02, -8.58993667e-02,\n", "        -8.61225237e-02, -8.58689770e-02, -8.38448163e-02,\n", "        -8.16416483e-02, -7.87314013e-02, -7.56841560e-02,\n", "        -7.20600755e-02, -6.83960600e-02, -6.45523374e-02,\n", "        -6.07152154e-02, -5.69099318e-02, -5.31401456e-02,\n", "        -4.95545022e-02, -4.60129535e-02, -4.27183352e-02,\n", "        -3.94718606e-02, -3.65176870e-02, -3.36089390e-02,\n", "        -3.10006988e-02, -2.84337244e-02, -2.61659280e-02,\n", "        -2.39329081e-02, -2.19781010e-02, -2.00515810e-02,\n", "        -1.83768173e-02, -1.67250159e-02, -1.53028383e-02,\n", "        -1.38988532e-02, -1.27018085e-02, -1.15183527e-02,\n", "        -1.05134940e-02, -9.51828634e-03, -8.67266755e-03,\n", "        -7.83453654e-03, -7.13717569e-03, -6.44449609e-03,\n", "        -5.86299591e-03, -5.28455740e-03, -4.80409207e-03,\n", "        -4.32532397e-03, -3.92887154e-03, -3.53313460e-03,\n", "        -3.20751645e-03, -2.88189831e-03]])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# The following line resmaples the timecourse so that each timepoint will\n", "# correspond to 0.5 s instead of 1 s. \n", "\n", "tcs =np.array(tcs)\n", "\n", "x = np.linspace(0, np.shape(tcs)[1], num=100, endpoint=True)\n", "f = sc.interpolate.interp1d(x,tcs)\n", "x_new = np.linspace(0, np.shape(tcs)[1], num=TRs_after_resampling, endpoint=True)\n", "\n", "tcs_interp = f(x_new)\n", "\n", "plt.figure(figsize=(15, 6), dpi=80)\n", "\n", "\n", "plt.plot(x, np.transpose(tcs),'x')\n", "plt.plot(x_new, np.transpose(tcs_interp),'o')\n", "\n", "\n", "\n", "# Notice that the lenght of the tcs_interp is double the length of tcs.\n", "\n", "# With an interpolated timecourse now you can code your design matrix\n", "# correctly. The design matrix is going to consist of 200 columns were 1 will\n", "# specify the stimulus onset. Remember to specify the stimduration for\n", "# GLMsingle as 0.5 s instead of 1 s.\n", "\n", "print(np.shape(tcs))\n", "print(np.shape(tcs_interp))\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}