"""
Configuration module for GLMsingle PyTorch implementation.

This module provides configuration management for device selection, memory optimization,
and performance tuning parameters for the PyTorch-accelerated GLMsingle implementation.
"""

import torch
import time
import numpy as np
from typing import Optional, Union, Dict, Any


class GLMSingleConfig:
    """
    Configuration class for GLMsingle PyTorch implementation.
    
    This class manages device selection, memory optimization settings,
    and performance parameters for optimal GPU acceleration.
    
    Attributes:
        device (torch.device): Target device for computations (CPU/GPU)
        dtype (torch.dtype): Default tensor data type
        memory_efficient (bool): Enable memory optimization strategies
        auto_mixed_precision (bool): Use automatic mixed precision
        chunk_size_gpu (int): Chunk size for GPU processing
        chunk_size_cpu (int): Chunk size for CPU processing
    """
    
    def __init__(self, 
                 device: Optional[Union[str, torch.device]] = None,
                 dtype: torch.dtype = torch.float32,
                 memory_efficient: bool = True,
                 auto_mixed_precision: bool = False,
                 chunk_size_gpu: int = 100000,
                 chunk_size_cpu: int = 50000):
        """
        Initialize GLMSingle configuration.
        
        Args:
            device: Target device ('cpu', 'cuda', or torch.device)
            dtype: Default tensor data type
            memory_efficient: Enable memory optimization
            auto_mixed_precision: Use automatic mixed precision
            chunk_size_gpu: Chunk size for GPU processing
            chunk_size_cpu: Chunk size for CPU processing
        """
        self.device = self._setup_device(device)
        self.dtype = dtype
        self.memory_efficient = memory_efficient
        self.auto_mixed_precision = auto_mixed_precision and self.device.type == 'cuda'
        self.chunk_size_gpu = chunk_size_gpu
        self.chunk_size_cpu = chunk_size_cpu
        
        # Performance tracking
        self.timing_enabled = True
        self.memory_tracking = True
        
        # Default parameters (matching original GLMsingle)
        self.default_params = {
            'numforhrf': 50,
            'hrfthresh': 0.5,
            'hrffitmask': 1,
            'R2thresh': 0,
            'hrfmodel': 'optimise',
            'n_jobs': 1,
            'n_pcs': 10,
            'n_boots': 100,
            'extra_regressors': False,
            'wantlibrary': 1,
            'wantglmdenoise': 1,
            'wantfracridge': 1,
            'chunklen': self.chunk_size_gpu if self.device.type == 'cuda' else self.chunk_size_cpu,
            'wantfileoutputs': [1, 1, 1, 1],
            'wantmemoryoutputs': [1, 1, 1, 1],
            'wanthdf5': 0,
            'wantparametric': 0,
            'wantpercentbold': 1,
            'wantlss': 0,
            'brainthresh': [99.0, 0.1],
            'brainR2': [],
            'brainexclude': False,
            'pcR2cutoff': [],
            'pcR2cutoffmask': 1,
            'pcstop': 1.05,
            'fracs': np.linspace(1, 0.05, 20),
            'wantautoscale': 1,
            'seed': time.time(),
            'suppressoutput': 0,
            'lambda': 0,
            'firdelay': 30,
            'firpct': 99,
        }
    
    def _setup_device(self, device: Optional[Union[str, torch.device]]) -> torch.device:
        """
        Setup and validate the target device.
        
        Args:
            device: Requested device
            
        Returns:
            torch.device: Validated device
        """
        if device is None:
            # Auto-detect best available device
            if torch.cuda.is_available():
                device = torch.device('cuda')
                print(f"Auto-selected GPU: {torch.cuda.get_device_name()}")
            else:
                device = torch.device('cpu')
                print("CUDA not available, using CPU")
        elif isinstance(device, str):
            device = torch.device(device)
        
        # Validate device availability
        if device.type == 'cuda' and not torch.cuda.is_available():
            print("Warning: CUDA requested but not available, falling back to CPU")
            device = torch.device('cpu')
        
        return device
    
    @property
    def chunk_size(self) -> int:
        """Get appropriate chunk size for current device."""
        return self.chunk_size_gpu if self.device.type == 'cuda' else self.chunk_size_cpu
    
    def get_memory_info(self) -> Dict[str, Any]:
        """
        Get current memory information.
        
        Returns:
            Dict containing memory statistics
        """
        info = {}
        if self.device.type == 'cuda':
            info['gpu_memory_allocated'] = torch.cuda.memory_allocated(self.device)
            info['gpu_memory_reserved'] = torch.cuda.memory_reserved(self.device)
            info['gpu_max_memory_allocated'] = torch.cuda.max_memory_allocated(self.device)
        else:
            info['device'] = 'cpu'
        
        return info
    
    def optimize_for_dataset(self, data_shape: tuple, design_shape: tuple) -> None:
        """
        Optimize configuration based on dataset characteristics.
        
        Args:
            data_shape: Shape of fMRI data (voxels, timepoints)
            design_shape: Shape of design matrix (timepoints, conditions)
        """
        n_voxels = np.prod(data_shape[:-1])  # All dimensions except time
        n_timepoints = data_shape[-1]
        n_conditions = design_shape[-1]
        
        # Estimate memory requirements
        estimated_memory = n_voxels * n_timepoints * 4  # 4 bytes per float32
        
        if self.device.type == 'cuda':
            available_memory = torch.cuda.get_device_properties(self.device).total_memory
            # Use 80% of available memory as safety margin
            safe_memory = available_memory * 0.8
            
            if estimated_memory > safe_memory:
                # Adjust chunk size to fit in memory
                self.chunk_size_gpu = int(safe_memory / (n_timepoints * 4))
                print(f"Adjusted GPU chunk size to {self.chunk_size_gpu} for memory efficiency")
        
        print(f"Dataset optimization complete:")
        print(f"  - Voxels: {n_voxels:,}")
        print(f"  - Timepoints: {n_timepoints}")
        print(f"  - Conditions: {n_conditions}")
        print(f"  - Chunk size: {self.chunk_size:,}")
        print(f"  - Device: {self.device}")
