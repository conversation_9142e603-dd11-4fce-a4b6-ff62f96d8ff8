
<!DOCTYPE html
  PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
   <!--
This HTML was auto-generated from MATLAB code.
To make changes, update the MATLAB code and republish this document.
      --><title>Example 2 Overview</title><meta name="generator" content="MATLAB 9.4"><link rel="schema.DC" href="http://purl.org/dc/elements/1.1/"><meta name="DC.date" content="2021-08-05"><meta name="DC.source" content="example2.m"><style type="text/css">
html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,font,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td{margin:0;padding:0;border:0;outline:0;font-size:100%;vertical-align:baseline;background:transparent}body{line-height:1}ol,ul{list-style:none}blockquote,q{quotes:none}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none}:focus{outine:0}ins{text-decoration:none}del{text-decoration:line-through}table{border-collapse:collapse;border-spacing:0}

html { min-height:100%; margin-bottom:1px; }
html body { height:100%; margin:0px; font-family:Arial, Helvetica, sans-serif; font-size:10px; color:#000; line-height:140%; background:#fff none; overflow-y:scroll; }
html body td { vertical-align:top; text-align:left; }

h1 { padding:0px; margin:0px 0px 25px; font-family:Arial, Helvetica, sans-serif; font-size:1.5em; color:#d55000; line-height:100%; font-weight:normal; }
h2 { padding:0px; margin:0px 0px 8px; font-family:Arial, Helvetica, sans-serif; font-size:1.2em; color:#000; font-weight:bold; line-height:140%; border-bottom:1px solid #d6d4d4; display:block; }
h3 { padding:0px; margin:0px 0px 5px; font-family:Arial, Helvetica, sans-serif; font-size:1.1em; color:#000; font-weight:bold; line-height:140%; }

a { color:#005fce; text-decoration:none; }
a:hover { color:#005fce; text-decoration:underline; }
a:visited { color:#004aa0; text-decoration:none; }

p { padding:0px; margin:0px 0px 20px; }
img { padding:0px; margin:0px 0px 20px; border:none; }
p img, pre img, tt img, li img, h1 img, h2 img { margin-bottom:0px; } 

ul { padding:0px; margin:0px 0px 20px 23px; list-style:square; }
ul li { padding:0px; margin:0px 0px 7px 0px; }
ul li ul { padding:5px 0px 0px; margin:0px 0px 7px 23px; }
ul li ol li { list-style:decimal; }
ol { padding:0px; margin:0px 0px 20px 0px; list-style:decimal; }
ol li { padding:0px; margin:0px 0px 7px 23px; list-style-type:decimal; }
ol li ol { padding:5px 0px 0px; margin:0px 0px 7px 0px; }
ol li ol li { list-style-type:lower-alpha; }
ol li ul { padding-top:7px; }
ol li ul li { list-style:square; }

.content { font-size:1.2em; line-height:140%; padding: 20px; }

pre, code { font-size:12px; }
tt { font-size: 1.2em; }
pre { margin:0px 0px 20px; }
pre.codeinput { padding:10px; border:1px solid #d3d3d3; background:#f7f7f7; }
pre.codeoutput { padding:10px 11px; margin:0px 0px 20px; color:#4c4c4c; }
pre.error { color:red; }

@media print { pre.codeinput, pre.codeoutput { word-wrap:break-word; width:100%; } }

span.keyword { color:#0000FF }
span.comment { color:#228B22 }
span.string { color:#A020F0 }
span.untermstring { color:#B20000 }
span.syscmd { color:#B28C00 }

.footer { width:auto; padding:10px 0px; margin:25px 0px 0px; border-top:1px dotted #878787; font-size:0.8em; line-height:140%; font-style:italic; color:#878787; text-align:left; float:none; }
.footer p { margin:0px; }
.footer a { color:#878787; }
.footer a:hover { color:#878787; text-decoration:underline; }
.footer a:visited { color:#878787; }

table th { padding:7px 5px; text-align:left; vertical-align:middle; border: 1px solid #d6d4d4; font-weight:bold; }
table td { padding:7px 5px; text-align:left; vertical-align:top; border:1px solid #d6d4d4; }





  </style></head><body><div class="content"><h1>Example 2 Overview</h1><!--introduction--><p>GLMsingle is new tool that provides efficient, scalable, and accurate single-trial fMRI response estimates.</p><p>The purpose of this Example 2 notebook is to guide the user through basic calls to GLMsingle, using a representative, small-scale test dataset (in this case, 4 runs from an fMRI localizer session containing a block design, which was part of the Natural Scenes Dataset).</p><p>The goal is to examine the effect of GLMsingle on the reliability of fMRI response estimates to the different conditions used in the localizer set (e.g. faces, bodies, objects, scenes, words). By default, the tool implements a set of optimizations that improve upon generic GLM approaches by: (1) identifying an optimal hemodynamic response function (HRF) at each voxel, (2) deriving a set of useful GLM nuisance regressors via "GLMdenoise" and picking an optimal number to include in the final GLM, and (3) applying a custom amount of ridge regularization at each voxel using an efficient technique called "fracridge". The output of GLMsingle are GLM betas reflecting the estimated percent signal change in each voxel in response to each experimental stimulus or condition being modeled.</p><p>Beyond directly improving the reliability of neural responses to repeated conditions, these optimized techniques for signal estimation can have a range of desirable downstream effects such as: improving cross-subject representational similarity within and between datasets; improving the single-image decodability of evoked neural patterns via MVPA; and, decreasing the correlation in spatial patterns observed at neighboring timepoints in analysis of fMRI GLM outputs. See our video presentation at V-VSS 2020 for a summary of these phenomena as observed in recent massive-scale fMRI datasets (the Natural Scenes Dataset and BOLD5000): https://www.youtube.com/watch?v=yb3Nn7Han8o</p><p>Example 2 contains a full walkthrough of the process of loading an example dataset and design matrix, estimating neural responses using GLMsingle, estimating the reliability of responses at each voxel, and comparing those achieved via GLMsingle to those achieved using a baseline GLM. After loading and visualizing formatted fMRI time-series and their corresponding design matrices, we will describe the default behavior of GLMsingle and show how to modify hyperparameters if the user desires. Throughout the notebook we will highlight important metrics and outputs using figures, print statements, and comments.</p><p>Users encountering bugs, unexpected outputs, or other issues regarding GLMsingle shouldn't hesitate to raise an issue on GitHub: https://github.com/kendrickkay/GLMsingle/issues</p><!--/introduction--><h2>Contents</h2><div><ul><li><a href="#1">Add dependencies and download the data</a></li><li><a href="#2">Data overview</a></li><li><a href="#6">Call GLMestimatesingletrial with default parameters</a></li><li><a href="#7">Summary of important outputs</a></li><li><a href="#8">Plot a slice of brain with GLMsingle outputs</a></li><li><a href="#9">Run a baseline GLM to compare with GLMsingle.</a></li><li><a href="#11">Organize GLM outputs to enable calculation of voxel reliability</a></li><li><a href="#14">Compute median split-half reliability for each GLM version</a></li><li><a href="#15">Compare visual voxel reliabilities between beta versions in V1 and FFA ROIs</a></li><li><a href="#16">Summary: median ROI voxel reliability for each beta version</a></li></ul></div><h2 id="1">Add dependencies and download the data</h2><pre class="codeinput"><span class="comment">% We will assume that the current working directory is the directory that</span>
<span class="comment">% contains this script.</span>

<span class="comment">% Add path to GLMsingle</span>
addpath(genpath(<span class="string">'../../matlab'</span>));

<span class="comment">% You also need fracridge repository to run this code. For example, you</span>
<span class="comment">% could do:</span>
<span class="comment">%   git clone https://github.com/nrdg/fracridge.git</span>
<span class="comment">% and then do:</span>
<span class="comment">%   addpath('fracridge')</span>

<span class="comment">% Start fresh</span>
clear
clc
close <span class="string">all</span>

<span class="comment">% Name of directory to which outputs will be saved</span>
outputdir = <span class="string">'example2outputs'</span>;

<span class="comment">% Download files to data directory</span>
<span class="keyword">if</span> ~exist(<span class="string">'./data'</span>,<span class="string">'dir'</span>)
    mkdir(<span class="string">'data'</span>)
<span class="keyword">end</span>

<span class="keyword">if</span>  ~exist(<span class="string">'./data/nsdflocexampledataset.mat'</span>,<span class="string">'file'</span>)
    <span class="comment">% download data with curl</span>
    system(<span class="string">'curl -L --output ./data/nsdflocexampledataset.mat https://osf.io/g42tm/download'</span>)
<span class="keyword">end</span>
load(<span class="string">'./data/nsdflocexampledataset.mat'</span>)
<span class="comment">% Data comes from the NSD dataset (subj01, floc experiment, runs 1-4).</span>
<span class="comment">% https://www.biorxiv.org/content/10.1101/2021.02.22.432340v1.full.pdf</span>
</pre><h2 id="2">Data overview</h2><pre class="codeinput">clc
whos

<span class="comment">% data -&gt; consists of several runs of 4D volume files (x,y,z,t)  where</span>
<span class="comment">% (t)ime is the 4th dimention.</span>

<span class="comment">% visual.ROI -&gt; maskfile defining different regions of primary visual</span>
<span class="comment">% cortex, where (x,y,z) = integers 1 through 7 defines sets of voxels</span>
<span class="comment">% belonging to different anatomical subsets (e.g. idx 1 corresponds to V1).</span>
<span class="comment">% In this example, we will plot reliability values from voxels in V1.</span>

<span class="comment">% floc.ROI -&gt; maskfile containing manually-defined face-selective cortical</span>
<span class="comment">% ROIs, where (x,y,z) = integers 1 through 3 defines sets of voxels</span>
<span class="comment">% belonging to distinct ROIs (e.g. idx 1 corresponds to OFA, idx 2 to</span>
<span class="comment">% FFA-1). In this example we will plot reliability values from voxels in</span>
<span class="comment">% FFA.</span>

fprintf(<span class="string">'There are %d runs in total.\n'</span>,length(design));
fprintf(<span class="string">'The dimensions of the data for the first run are %s.\n'</span>,mat2str(size(data{1})));
fprintf(<span class="string">'The stimulus duration is %.3f seconds.\n'</span>,stimdur);
fprintf(<span class="string">'The sampling rate (TR) is %.3f seconds.\n'</span>,tr);
</pre><pre class="codeoutput">  Name           Size                Bytes  Class     Attributes

  data           1x4             662329024  cell                
  design         1x4                  4640  cell                
  floc           1x1               1415408  struct              
  outputdir      1x15                   30  char                
  stimdur        1x1                     8  double              
  tr             1x1                     8  double              
  visual         1x1               1415408  struct              

There are 4 runs in total.
The dimensions of the data for the first run are [52 81 42 234].
The stimulus duration is 4.000 seconds.
The sampling rate (TR) is 1.333 seconds.
</pre><pre class="codeinput">figure(1);clf

<span class="comment">%Show example design matrix.</span>
<span class="keyword">for</span> d = 1:length(design)
    subplot(2,2,d)
    imagesc(design{d}); colormap <span class="string">gray</span>; drawnow
    xlabel(<span class="string">'Conditions'</span>)
    ylabel(<span class="string">'TRs'</span>)
    title(sprintf(<span class="string">'Design matrix for run%i'</span>,d))
<span class="keyword">end</span>
</pre><img vspace="5" hspace="5" src="example2_01.png" alt=""> <pre class="codeinput"><span class="comment">% design -&gt; Each run has a corresponding design matrix where each column</span>
<span class="comment">% describes a single condition (conditions are repeated across runs). Each</span>
<span class="comment">% design matrix is binary with 1 specfing the time (TR) when the stimulus</span>
<span class="comment">% is presented on the screen.</span>
<span class="comment">%</span>
<span class="comment">% In each run of the NSD floc experiment there were images from 10</span>
<span class="comment">% categories presented in blocks of duration 4 sec each. Each category in</span>
<span class="comment">% the localizer set was presented for 6 blocks per run, and each block</span>
<span class="comment">% contained 8 stimuli shown for 0.5 sec each. The individual blocks are</span>
<span class="comment">% coded as single events in the design matrices used for the present GLM</span>
<span class="comment">% example. As such, there are 10 predictor columns/conditions per run, with</span>
<span class="comment">% the onset TRs for each of the 6 presentations indicated in the design</span>
<span class="comment">% matrix. Notice that the condition order is pseudo-randomized. Stimulus</span>
<span class="comment">% details are described here: https://github.com/VPNL/fLoc</span>
</pre><pre class="codeinput"><span class="comment">% Show an example slice of the first fMRI volume.</span>
figure(2);clf

imagesc(makeimagestack(data{1}(:,:,:,1)));
colormap(gray);
axis <span class="string">equal</span> <span class="string">tight</span>;
c=colorbar;
title(<span class="string">'fMRI data (first volume)'</span>);
set(gcf,<span class="string">'Position'</span>,[418   412   782   405])
axis <span class="string">off</span>
c.Label.String = <span class="string">'T2*w intensity'</span>;
set(gca,<span class="string">'FontSize'</span>,15)
</pre><img vspace="5" hspace="5" src="example2_02.png" alt=""> <h2 id="6">Call GLMestimatesingletrial with default parameters</h2><pre class="codeinput"><span class="comment">% Outputs and figures will be stored in a folder (you can specify its name</span>
<span class="comment">% as the 5th output to GLMestimatesingletrial). Model estimates can be also</span>
<span class="comment">% saved to the 'results' variable which is the only output of</span>
<span class="comment">% GLMestimatesingletrial.</span>

<span class="comment">% Optional parameters below can be assigned to a structure, i.e., opt =</span>
<span class="comment">% struct('wantlibrary',1,'wantglmdenoise',1); Options are the 6th input to</span>
<span class="comment">% GLMestimatesingletrial.</span>

<span class="comment">% There are many options that can be specified; here, we comment on the</span>
<span class="comment">% main options that one might want to modify/set. Defaults for the options</span>
<span class="comment">% are indicated below.</span>

<span class="comment">% wantlibrary = 1 -&gt; Fit HRF to each voxel wantglmdenoise = 1 -&gt; Use</span>
<span class="comment">% GLMdenoise wantfracridge = 1  -&gt; Use ridge regression to improve beta</span>
<span class="comment">% estimates chunknum = 50000 -&gt; is the number of voxels that we will</span>
<span class="comment">% process at the</span>
<span class="comment">%   same time. For setups with lower memory, you may need to decrease this</span>
<span class="comment">%   number.</span>

<span class="comment">% wantmemoryoutputs is a logical vector [A B C D] indicating which of the</span>
<span class="comment">%     four model types to return in the output &lt;results&gt;. The user must be</span>
<span class="comment">%     careful with this, as large datasets can require a lot of RAM. If you</span>
<span class="comment">%     do not request the various model types, they will be cleared from</span>
<span class="comment">%     memory (but still potentially saved to disk). Default: [0 0 0 1]</span>
<span class="comment">%     which means return only the final type-D model.</span>

<span class="comment">% wantfileoutputs is a logical vector [A B C D] indicating which of the</span>
<span class="comment">%     four model types to save to disk (assuming that they are computed). A</span>
<span class="comment">%     = 0/1 for saving the results of the ONOFF model B = 0/1 for saving</span>
<span class="comment">%     the results of the FITHRF model C = 0/1 for saving the results of the</span>
<span class="comment">%     FITHRF_GLMdenoise model D = 0/1 for saving the results of the</span>
<span class="comment">%     FITHRF_GLMdenoise_RR model Default: [1 1 1 1] which means save all</span>
<span class="comment">%     computed results to disk.</span>

<span class="comment">% numpcstotry (optional) is a non-negative integer indicating the maximum</span>
<span class="comment">%     number of PCs to enter into the model. Default: 10.</span>

<span class="comment">% fracs (optional) is a vector of fractions that are greater than 0</span>
<span class="comment">%     and less than or equal to 1. We automatically sort in descending</span>
<span class="comment">%     order and ensure the fractions are unique. These fractions indicate</span>
<span class="comment">%     the regularization levels to evaluate using fractional ridge</span>
<span class="comment">%     regression (fracridge) and cross-validation. Default:</span>
<span class="comment">%     fliplr(.05:.05:1). A special case is when &lt;fracs&gt; is specified as a</span>
<span class="comment">%     single scalar value. In this case, cross-validation is NOT performed</span>
<span class="comment">%     for the type-D model, and we instead blindly use the supplied</span>
<span class="comment">%     fractional value for the type-D model.</span>

<span class="comment">% For the purpose of this example we will keep all outputs in the memory.</span>
opt = struct(<span class="string">'wantmemoryoutputs'</span>,[1 1 1 1]);

<span class="comment">% This example saves output .mat files to the folder</span>
<span class="comment">% "example2outputs/GLMsingle". If these outputs don't already exist, we</span>
<span class="comment">% will perform the time-consuming call to GLMestimatesingletrial.m;</span>
<span class="comment">% otherwise, we will just load from disk.</span>
<span class="keyword">if</span> ~exist([outputdir <span class="string">'/GLMsingle'</span>],<span class="string">'dir'</span>)

    [results] = GLMestimatesingletrial(design,data,stimdur,tr,[outputdir <span class="string">'/GLMsingle'</span>],opt);

    <span class="comment">% We assign outputs of GLMestimatesingletrial to "models" structure.</span>
    <span class="comment">% Note that results{1} contains GLM estimates from an ONOFF model,</span>
    <span class="comment">% where all images are treated as the same condition. These estimates</span>
    <span class="comment">% could be potentially used to find cortical areas that respond to</span>
    <span class="comment">% visual stimuli. We want to compare beta weights between conditions</span>
    <span class="comment">% therefore we are not going to store the ONOFF GLM results.</span>

    clear <span class="string">models</span>;
    models.FIT_HRF = results{2};
    models.FIT_HRF_GLMdenoise = results{3};
    models.FIT_HRF_GLMdenoise_RR = results{4};

<span class="keyword">else</span>
    <span class="comment">% Load existing file outputs if they exist</span>
    results = load([outputdir <span class="string">'/GLMsingle/TYPEB_FITHRF.mat'</span>]);
    models.FIT_HRF = results;
    results = load([outputdir <span class="string">'/GLMsingle/TYPEC_FITHRF_GLMDENOISE.mat'</span>]);
    models.FIT_HRF_GLMdenoise = results;
    results = load([outputdir <span class="string">'/GLMsingle/TYPED_FITHRF_GLMDENOISE_RR.mat'</span>]);
    models.FIT_HRF_GLMdenoise_RR = results;
<span class="keyword">end</span>
</pre><h2 id="7">Summary of important outputs</h2><pre class="codeinput"><span class="comment">% The outputs of GLMestimatesingletrial.m are formally documented in its</span>
<span class="comment">% header. Here, we highlight a few of the more important outputs:</span>
<span class="comment">%</span>
<span class="comment">% R2 -&gt; is model accuracy expressed in terms of R^2 (percentage).</span>
<span class="comment">%</span>
<span class="comment">% modelmd -&gt; is the full set of single-trial beta weights (X x Y x Z x</span>
<span class="comment">% TRIALS). Beta weights are arranged in chronological order.</span>
<span class="comment">%</span>
<span class="comment">% HRFindex -&gt; is the 1-index of the best fit HRF. HRFs can be recovered</span>
<span class="comment">% with getcanonicalHRFlibrary(stimdur,tr)</span>
<span class="comment">%</span>
<span class="comment">% FRACvalue -&gt; is the fractional ridge regression regularization level</span>
<span class="comment">% chosen for each voxel. Values closer to 1 mean less regularization.</span>
</pre><h2 id="8">Plot a slice of brain with GLMsingle outputs</h2><pre class="codeinput"><span class="comment">% We are going to plot several outputs from the FIT_HRF_GLMdenoise_RR GLM,</span>
<span class="comment">% which contains the full set of GLMsingle optimizations.</span>

slice_V1 = 20; <span class="comment">% slice containing many early visual cortex voxels</span>

<span class="comment">% we will plot betas, R2, optimal HRF indices, and the voxel frac values</span>
val2plot = {<span class="string">'modelmd'</span>;<span class="string">'R2'</span>;<span class="string">'HRFindex'</span>;<span class="string">'FRACvalue'</span>};
cmaps = {cmapsign2;hot;jet;copper};

<span class="comment">% Mask out voxels that are outside the brain</span>
brainmask = models.FIT_HRF_GLMdenoise_RR.meanvol(:,5:end-5,slice_V1) &gt; 250;

figure(3);clf

<span class="keyword">for</span> v = 1 : length(val2plot)
    f=subplot(2,2,v);

    <span class="comment">% Set non-brain voxels to nan to ease visualization</span>
    plotdata = models.FIT_HRF_GLMdenoise_RR.(val2plot{v})(:,5:end-5,slice_V1);
    plotdata(~brainmask) = nan;

    <span class="keyword">if</span> contains(<span class="string">'modelmd'</span>,val2plot{v})
        imagesc(nanmean(plotdata,4),[-5 5]); axis <span class="string">off</span> <span class="string">image</span>;
        title(<span class="string">'Average GLM betas (750 stimuli)'</span>)
    <span class="keyword">else</span>
        imagesc(plotdata); axis <span class="string">off</span> <span class="string">image</span>;
        title(val2plot{v})
    <span class="keyword">end</span>
    colormap(f,cmaps{v})
    colorbar
    set(gca,<span class="string">'FontSize'</span>,15)
<span class="keyword">end</span>

set(gcf,<span class="string">'Position'</span>,[418   412   782   605])
</pre><img vspace="5" hspace="5" src="example2_03.png" alt=""> <h2 id="9">Run a baseline GLM to compare with GLMsingle.</h2><pre class="codeinput"><span class="comment">% Additionally, for comparison purposes we are going to run a standard GLM</span>
<span class="comment">% without HRF fitting, GLMdenoise, or ridge regression regularization. We</span>
<span class="comment">% will change the default settings by using the "opt" structure.</span>

opt.wantlibrary = 0; <span class="comment">% switch off HRF fitting</span>
opt.wantglmdenoise = 0; <span class="comment">% switch off GLMdenoise</span>
opt.wantfracridge = 0; <span class="comment">% switch off ridge regression</span>
opt.wantfileoutputs = [0 1 0 0];
opt.wantmemoryoutputs = [0 1 0 0];

<span class="comment">% If these outputs don't already exist, we will perform the call to</span>
<span class="comment">% GLMestimatesingletrial.m; otherwise, we will just load from disk.</span>
<span class="keyword">if</span> ~exist([outputdir <span class="string">'/GLMbaseline'</span>],<span class="string">'dir'</span>)

    [ASSUME_HRF] = GLMestimatesingletrial(design,data,stimdur,tr,[outputdir <span class="string">'/GLMbaseline'</span>],opt);
    models.ASSUME_HRF = ASSUME_HRF{2};

<span class="keyword">else</span>

    <span class="comment">% Note that even though we are loading TYPEB_FITHRF betas, HRF fitting</span>
    <span class="comment">% has been turned off and this struct field will thus contain the</span>
    <span class="comment">% outputs of a GLM fit using the canonical HRF.</span>
    results = load([outputdir <span class="string">'/GLMbaseline/TYPEB_FITHRF.mat'</span>]);
    models.ASSUME_HRF = results;

<span class="keyword">end</span>

<span class="comment">% We assign outputs from GLMestimatesingletrial to "models" structure.</span>
<span class="comment">% Again, results{1} contains GLM estimates from an ONOFF model so we are</span>
<span class="comment">% not going to extract it.</span>
</pre><pre class="codeinput"><span class="comment">% Now, "models" variable holds solutions for 4 GLM models</span>
disp(fieldnames(models))
</pre><pre class="codeoutput">    'FIT_HRF'
    'FIT_HRF_GLMdenoise'
    'FIT_HRF_GLMdenoise_RR'
    'ASSUME_HRF'

</pre><h2 id="11">Organize GLM outputs to enable calculation of voxel reliability</h2><pre class="codeinput"><span class="comment">% To compare the results of different GLMs we are going to calculate the</span>
<span class="comment">% voxel-wise split-half reliablity for each model. Reliability values</span>
<span class="comment">% reflect a correlation between beta weights for repeated presentations of</span>
<span class="comment">% the same conditions. In short, we are going to check how</span>
<span class="comment">% reliable/reproducible are the single trial responses to repeated</span>
<span class="comment">% conditions estimated with each GLM type.</span>

<span class="comment">% This NSD floc scan session has 6 repetitions of each condition per run.</span>
<span class="comment">% In the code below, we are attempting to locate the indices in the beta</span>
<span class="comment">% weight GLMsingle outputs modelmd(x,y,z,trials) that correspond to</span>
<span class="comment">% repeated conditions.</span>

<span class="comment">% Consolidate design matrices</span>
designALL = cat(1,design{:});

<span class="comment">% Construct a vector containing 1-indexed condition numbers in</span>
<span class="comment">% chronological order.</span>

corder = [];
<span class="keyword">for</span> p=1:size(designALL,1)
    <span class="keyword">if</span> any(designALL(p,:))
        corder = [corder find(designALL(p,:))];
    <span class="keyword">end</span>
<span class="keyword">end</span>
</pre><pre class="codeinput"><span class="comment">% let's take a look at the first few entries</span>
corder(1:3)

<span class="comment">% Note that [10 8 7] means that the first stimulus block involved</span>
<span class="comment">% presentation of the 10th condition, the second stimulus block involved</span>
<span class="comment">% presentation of the 8th condition, and so on.</span>
</pre><pre class="codeoutput">
ans =

    10     8     7

</pre><pre class="codeinput"><span class="comment">% In order to compute split-half reliability, we have to do some indexing.</span>
<span class="comment">% We want to find all repetitions of the same condition. For example we can</span>
<span class="comment">% look up when during the 4 blocks image 1 was repeated. Each condition</span>
<span class="comment">% should be repeated exactly 24 times.</span>

fprintf(<span class="string">'Condition 1 was repeated %i times across 4 runs, with GLMsingle betas at the following indices:\n'</span>,length(find(corder==1)));
find(corder==1)

<span class="comment">% Now, for each voxel we are going to correlate beta weights describing the</span>
<span class="comment">% response to averaged even presentations of the 10 conditions with beta</span>
<span class="comment">% weights describing the responses from the averaged odd repetitions of the</span>
<span class="comment">% same conditions, over time. With 10 conditions in the localizer set, the</span>
<span class="comment">% split-half correlation for each voxel will thus reflect the relationship</span>
<span class="comment">% between two vectors with 10 beta weights each.</span>
</pre><pre class="codeoutput">Condition 1 was repeated 24 times across 4 runs, with GLMsingle betas at the following indices:

ans =

  Columns 1 through 13

     8    13    17    20    34    57    64    76    85    97   106   114   126

  Columns 14 through 24

   137   142   151   171   174   195   202   204   214   227   237

</pre><h2 id="14">Compute median split-half reliability for each GLM version</h2><pre class="codeinput"><span class="comment">% To calculate the split-half reliability we are going to average the odd</span>
<span class="comment">% and even beta weights extracted from the same condition and calculate the</span>
<span class="comment">% correlation coefficent between these values. We do this for each voxel</span>
<span class="comment">% inside the primary visual cortex and face-selective cortical ROIs.</span>

<span class="comment">% We first arrange models from least to most sophisticated (for</span>
<span class="comment">% visualization purposes)</span>
model_names = fieldnames(models);
model_names = model_names([4 1 2 3]);

<span class="comment">% Create output variable for reliability values</span>
vox_reliabilities = cell(1,length(models));

<span class="comment">% For each GLM...</span>
<span class="keyword">for</span> m = 1 : length(model_names)

    <span class="comment">% Get the GLM betas</span>
    modelmd = models.(model_names{m}).modelmd;

    dims = size(modelmd);
    Xdim = dims(1);
    Ydim = dims(2);
    Zdim = dims(3);

    cond = size(design{1},2);
    reps = dims(4)/cond;

    <span class="comment">% Create an empty variable for storing betas grouped together by</span>
    <span class="comment">% condition (X, Y, Z, nReps, nConditions)</span>
    betas = nan(Xdim,Ydim,Zdim,reps,cond);

    <span class="comment">% Populate repetition beta variable by iterating through conditions</span>
    <span class="keyword">for</span> c = 1 : length(unique(corder))

        indx = find(corder == c);
        betas(:,:,:,:,c) = modelmd(:,:,:,indx);

    <span class="keyword">end</span>

    <span class="comment">% Output variable for reliability values</span>
    vox_reliability = NaN(Xdim, Ydim, Zdim);

    <span class="comment">% Loop through voxels in the fMRI volume</span>
    <span class="keyword">for</span> i = 1:Xdim
        <span class="keyword">for</span> j = 1:Ydim
            <span class="keyword">for</span> k = 1:Zdim

                <span class="comment">% Calculate the reliability only for voxels within the</span>
                <span class="comment">% EarlyVis and FFA ROIs, for the sake of efficiency</span>
                <span class="keyword">if</span> visual.ROI(i,j,k) &gt; 0 || floc.ROI(i,j,k) &gt; 0

                    vox_data  = squeeze(betas(i,j,k,:,:));
                    even_data = nanmean(vox_data(1:2:end,:));
                    odd_data  = nanmean(vox_data(2:2:end,:));

                    <span class="comment">% Reliability is the split-half correlation between odd</span>
                    <span class="comment">% and even presentations</span>
                    vox_reliability(i,j,k) = corr(even_data', odd_data');

                <span class="keyword">end</span>
            <span class="keyword">end</span>
        <span class="keyword">end</span>
    <span class="keyword">end</span>

    <span class="comment">% Store reliablity for each model</span>
    vox_reliabilities{m} = vox_reliability;

<span class="keyword">end</span>
</pre><h2 id="15">Compare visual voxel reliabilities between beta versions in V1 and FFA ROIs</h2><pre class="codeinput">figure(5);clf

<span class="comment">% For each GLM type we will calculate median reliability for voxels within</span>
<span class="comment">% the visual ROIs and draw a bar plot for FFA and V1.</span>

slice_V1 = 10; <span class="comment">% good slice for viewing V1</span>
slice_ffa = 3; <span class="comment">% good slice for viewing FFA</span>

<span class="keyword">for</span> s = 1 : 5

    subplot(2,5,s)
    underlay = data{1}(:,:,slice_V1,1);
    overlay  = visual.ROI(:,:,slice_V1)==1; <span class="comment">% index 1 corresponds to V1 in this mask variable</span>
    underlay_im = cmaplookup(underlay,min(underlay(:)),max(underlay(:)),[],gray(256));
    overlay_im = cmaplookup(overlay,-0.5,0.5,[],[0 0 1]);
    mask = visual.ROI(:,:,slice_V1)==1;

    hold <span class="string">on</span>
    imagesc(imrotate(underlay_im,180));
    imagesc(imrotate(overlay_im,180), <span class="string">'AlphaData'</span>, imrotate(mask,180));
    title(sprintf(<span class="string">'V1 voxels, slice = %i'</span>,slice_V1))
    slice_V1 = slice_V1 + 1;
    axis <span class="string">image</span>
    axis <span class="string">off</span>
    set(gca,<span class="string">'FontSize'</span>,14)

    subplot(2,5,s+5)
    underlay = data{1}(:,:,slice_ffa,1);
    overlay  = floc.ROI(:,:,slice_ffa)==2; <span class="comment">% index 2 corresponds to FFA in this mask variable</span>
    underlay_im = cmaplookup(underlay,min(underlay(:)),max(underlay(:)),[],gray(256));
    overlay_im = cmaplookup(overlay,-0.5,0.5,[],round([237 102 31]/255,2));
    mask = floc.ROI(:,:,slice_ffa)==2;

    hold <span class="string">on</span>
    imagesc(imrotate(underlay_im,180));
    imagesc(imrotate(overlay_im,180), <span class="string">'AlphaData'</span>, imrotate(mask,180));
    title(sprintf(<span class="string">'FFA voxels, slice = %i'</span>,slice_ffa))
    slice_ffa = slice_ffa + 1;
    axis <span class="string">image</span>
    axis <span class="string">off</span>
    set(gca,<span class="string">'FontSize'</span>,14)

<span class="keyword">end</span>

set(gcf,<span class="string">'Position'</span>,[218   212   1400   500])
</pre><img vspace="5" hspace="5" src="example2_04.png" alt=""> <h2 id="16">Summary: median ROI voxel reliability for each beta version</h2><pre class="codeinput"><span class="comment">% To perform a comparison between all GLM versions available in our visual</span>
<span class="comment">% ROIs, we will simply loop through the beta versions, isolate the voxels</span>
<span class="comment">% of interest, compute the median split-half correlation value within each</span>
<span class="comment">% ROI, and plot using a bar graph.</span>

figure(6)

cmap = [0.2314    0.6039    0.6980
    0.8615    0.7890    0.2457
    0.8824    0.6863         0
    0.9490    0.1020         0];

<span class="comment">% For each GLM type we calculate median reliability for voxels within V1</span>
<span class="comment">% and FFA</span>
plot_data = zeros(length(vox_reliabilities),2);
<span class="keyword">for</span> m = 1 : 4

    vox_reliability = vox_reliabilities{m};

    <span class="comment">% floc.ROI==2 points to FFA voxels, visual.ROI==1 points to V1 voxels</span>
    plot_data(m,:) = [nanmedian(vox_reliability(floc.ROI==2)) nanmedian(vox_reliability(visual.ROI==1))];

<span class="keyword">end</span>

bar(plot_data)
ylabel(<span class="string">'Median reliability'</span>)
set(gca,<span class="string">'Fontsize'</span>,12)
set(gca,<span class="string">'TickLabelInterpreter'</span>,<span class="string">'none'</span>)
xtickangle(0)
legend({<span class="string">'FFA'</span>;<span class="string">'V1'</span>},<span class="string">'Interpreter'</span>,<span class="string">'None'</span>,<span class="string">'Location'</span>,<span class="string">'NorthWest'</span>)
set(gcf,<span class="string">'Position'</span>,[418   412   1000   605])
title(<span class="string">'Median voxel split-half reliability of GLM models'</span>)
xticklabels(model_names')
set(gca,<span class="string">'FontSize'</span>,14)

<span class="comment">% Notice that there is systematic increase in reliability moving from the</span>
<span class="comment">% first to the second to the third to the final fourth version of the GLM</span>
<span class="comment">% results, within both the early visual and face-selective ROIs. These</span>
<span class="comment">% increases reflect, respectively, the addition of HRF fitting, the</span>
<span class="comment">% derivation and use of data-driven nuisance regressors, and the use of</span>
<span class="comment">% ridge regression as a way to regularize the instability of closely spaced</span>
<span class="comment">% experimental trials. Depending on one's experimental goals, it is</span>
<span class="comment">% possible with setting of option flags to activate a subset of these</span>
<span class="comment">% analysis features.</span>
<span class="comment">%</span>
<span class="comment">% Also, keep in mind that in the above figure, we are simply showing the</span>
<span class="comment">% median as a metric of the central tendency (you may want to peruse</span>
<span class="comment">% individual voxels in scatter plots, for example).</span>
<span class="comment">%</span>
<span class="comment">% Note also that median reliability is consistently higher in the FFA ROI</span>
<span class="comment">% than in the V1 ROI in this analysis of localizer data. Importantly, this</span>
<span class="comment">% does not imply that V1 data are inherently noisy or unreliable across the</span>
<span class="comment">% NSD dataset (e.g. in the NSD core experiment). Here we are analyzing GLM</span>
<span class="comment">% outputs from the localizer dataset, which was designed to identify neural</span>
<span class="comment">% ROIs whose voxel tuning profiles vary meaningfully across a small,</span>
<span class="comment">% curated set of high-level stimulus categories. As such, we would expect</span>
<span class="comment">% the FFA voxels to have relatively more well-defined tuning curves, with</span>
<span class="comment">% more substantial variability in GLM betas between the localizer</span>
<span class="comment">% categories, than the V1 voxels, whose tuning is determined by lower-level</span>
<span class="comment">% image features that do not necessarily vary in systematic ways between</span>
<span class="comment">% the localizer categories. For these reasons, one might expect that</span>
<span class="comment">% split-half correlations would be somewhat lower in the V1 voxels using</span>
<span class="comment">% our particular metric of reliability (odd-even correlation) in this</span>
<span class="comment">% dataset. Different metrics of reliability may generate different patterns</span>
<span class="comment">% of results when comparing these two ROIs, and we would also expect</span>
<span class="comment">% results to vary depending on the stimulus set.</span>
</pre><img vspace="5" hspace="5" src="example2_05.png" alt=""> <pre class="codeinput"><span class="comment">% We now plot the improvement of reliability when comparing</span>
<span class="comment">% FIT_HRF_GLMDENOISE_RR with ASSUME_HRF, with higher positive values</span>
<span class="comment">% reflecting greater benefit from applying GLMsingle.</span>

figure(7)

<span class="comment">% Comparison is the final output (FIT_HRF_GLMDENOISE_RR) vs. the baseline</span>
<span class="comment">% GLM (ASSUME_HRF)</span>
vox_improvement = vox_reliabilities{4} - vox_reliabilities{1};

slice = 4;

ROI = visual.ROI == 1 | floc.ROI == 2;

<span class="keyword">for</span> s = 1:15

    subplot(3,5,s)
    underlay = data{1}(:,5:end-5,slice,1);
    overlay  = vox_improvement(:,5:end-5,slice);
    underlay_im = cmaplookup(underlay,min(underlay(:)),max(underlay(:)),[],gray(256));
    overlay_im = cmaplookup(overlay,-0.3,0.3,[],cmapsign2);
    mask = ROI(:,5:end-5,slice)==1;
    hold <span class="string">on</span>
    imagesc(imrotate(underlay_im,180));
    imagesc(imrotate(overlay_im,180), <span class="string">'AlphaData'</span>, imrotate(mask,180));
    title([<span class="string">'median ROI \Delta{\itr} in slice:'</span> newline <span class="string">'+'</span> num2str(nanmedian(overlay(:)),3)])
    slice = slice + 1;
    axis <span class="string">image</span>
    colormap(cmapsign2)
    c = colorbar;
    c.TickLabels = {<span class="string">'-0.3'</span>;<span class="string">'0'</span>;<span class="string">'0.3'</span>};
    xticks([])
    yticks([])
    set(gca,<span class="string">'FontSize'</span>,14)
<span class="keyword">end</span>

set(gcf,<span class="string">'Position'</span>,[100   100   1600   700])
</pre><img vspace="5" hspace="5" src="example2_06.png" alt=""> <p class="footer"><br><a href="https://www.mathworks.com/products/matlab/">Published with MATLAB&reg; R2018a</a><br></p></div><!--
##### SOURCE BEGIN #####
%% Example 2 Overview
%
% GLMsingle is new tool that provides efficient, scalable, and accurate
% single-trial fMRI response estimates.
%
% The purpose of this Example 2 notebook is to guide the user through basic
% calls to GLMsingle, using a representative, small-scale test dataset (in
% this case, 4 runs from an fMRI localizer session containing a block
% design, which was part of the Natural Scenes Dataset).
%
% The goal is to examine the effect of GLMsingle on the reliability of
% fMRI response estimates to the different conditions used in the localizer set 
% (e.g. faces, bodies, objects, scenes, words). By default, the tool implements a
% set of optimizations that improve upon generic GLM approaches by: (1)
% identifying an optimal hemodynamic response function (HRF) at each voxel,
% (2) deriving a set of useful GLM nuisance regressors via "GLMdenoise" and
% picking an optimal number to include in the final GLM, and (3) applying a
% custom amount of ridge regularization at each voxel using an efficient
% technique called "fracridge". The output of GLMsingle are GLM betas
% reflecting the estimated percent signal change in each voxel in response
% to each experimental stimulus or condition being modeled.
%
% Beyond directly improving the reliability of neural responses to repeated
% conditions, these optimized techniques for signal estimation can have a
% range of desirable downstream effects such as: improving cross-subject
% representational similarity within and between datasets; improving the
% single-image decodability of evoked neural patterns via MVPA; and,
% decreasing the correlation in spatial patterns observed at neighboring
% timepoints in analysis of fMRI GLM outputs. See our video presentation at
% V-VSS 2020 for a summary of these phenomena as observed in recent
% massive-scale fMRI datasets (the Natural Scenes Dataset and BOLD5000): 
% https://www.youtube.com/watch?v=yb3Nn7Han8o
%
% Example 2 contains a full walkthrough of the process of loading an
% example dataset and design matrix, estimating neural responses using
% GLMsingle, estimating the reliability of responses at each voxel, and
% comparing those achieved via GLMsingle to those achieved using a baseline
% GLM. After loading and visualizing formatted fMRI time-series and their
% corresponding design matrices, we will describe the default behavior of
% GLMsingle and show how to modify hyperparameters if the user desires.
% Throughout the notebook we will highlight important metrics and outputs
% using figures, print statements, and comments.
%
% Users encountering bugs, unexpected outputs, or other issues regarding
% GLMsingle shouldn't hesitate to raise an issue on GitHub:
% https://github.com/kendrickkay/GLMsingle/issues

%% Add dependencies and download the data

% We will assume that the current working directory is the directory that
% contains this script.

% Add path to GLMsingle
addpath(genpath('../../matlab'));

% You also need fracridge repository to run this code. For example, you
% could do:
%   git clone https://github.com/nrdg/fracridge.git
% and then do:
%   addpath('fracridge')

% Start fresh
clear
clc
close all

% Name of directory to which outputs will be saved
outputdir = 'example2outputs';

% Download files to data directory
if ~exist('./data','dir')
    mkdir('data')
end

if  ~exist('./data/nsdflocexampledataset.mat','file')
    % download data with curl
    system('curl -L REPLACE_WITH_DASH_DASHoutput ./data/nsdflocexampledataset.mat https://osf.io/g42tm/download')
end
load('./data/nsdflocexampledataset.mat')
% Data comes from the NSD dataset (subj01, floc experiment, runs 1-4).
% https://www.biorxiv.org/content/10.1101/2021.02.22.432340v1.full.pdf

%% Data overview
clc
whos

% data -> consists of several runs of 4D volume files (x,y,z,t)  where
% (t)ime is the 4th dimention.

% visual.ROI -> maskfile defining different regions of primary visual
% cortex, where (x,y,z) = integers 1 through 7 defines sets of voxels
% belonging to different anatomical subsets (e.g. idx 1 corresponds to V1).
% In this example, we will plot reliability values from voxels in V1.

% floc.ROI -> maskfile containing manually-defined face-selective cortical
% ROIs, where (x,y,z) = integers 1 through 3 defines sets of voxels
% belonging to distinct ROIs (e.g. idx 1 corresponds to OFA, idx 2 to
% FFA-1). In this example we will plot reliability values from voxels in
% FFA.

fprintf('There are %d runs in total.\n',length(design));
fprintf('The dimensions of the data for the first run are %s.\n',mat2str(size(data{1})));
fprintf('The stimulus duration is %.3f seconds.\n',stimdur);
fprintf('The sampling rate (TR) is %.3f seconds.\n',tr);

%%

figure(1);clf

%Show example design matrix.
for d = 1:length(design)
    subplot(2,2,d)
    imagesc(design{d}); colormap gray; drawnow
    xlabel('Conditions')
    ylabel('TRs')
    title(sprintf('Design matrix for run%i',d))
end

%%

% design -> Each run has a corresponding design matrix where each column
% describes a single condition (conditions are repeated across runs). Each
% design matrix is binary with 1 specfing the time (TR) when the stimulus
% is presented on the screen.
% 
% In each run of the NSD floc experiment there were images from 10
% categories presented in blocks of duration 4 sec each. Each category in
% the localizer set was presented for 6 blocks per run, and each block
% contained 8 stimuli shown for 0.5 sec each. The individual blocks are
% coded as single events in the design matrices used for the present GLM
% example. As such, there are 10 predictor columns/conditions per run, with
% the onset TRs for each of the 6 presentations indicated in the design
% matrix. Notice that the condition order is pseudo-randomized. Stimulus
% details are described here: https://github.com/VPNL/fLoc

%%

% Show an example slice of the first fMRI volume.
figure(2);clf

imagesc(makeimagestack(data{1}(:,:,:,1)));
colormap(gray);
axis equal tight;
c=colorbar;
title('fMRI data (first volume)');
set(gcf,'Position',[418   412   782   405])
axis off
c.Label.String = 'T2*w intensity';
set(gca,'FontSize',15)

%% Call GLMestimatesingletrial with default parameters

% Outputs and figures will be stored in a folder (you can specify its name
% as the 5th output to GLMestimatesingletrial). Model estimates can be also
% saved to the 'results' variable which is the only output of
% GLMestimatesingletrial.

% Optional parameters below can be assigned to a structure, i.e., opt =
% struct('wantlibrary',1,'wantglmdenoise',1); Options are the 6th input to
% GLMestimatesingletrial.

% There are many options that can be specified; here, we comment on the
% main options that one might want to modify/set. Defaults for the options
% are indicated below.

% wantlibrary = 1 -> Fit HRF to each voxel wantglmdenoise = 1 -> Use
% GLMdenoise wantfracridge = 1  -> Use ridge regression to improve beta
% estimates chunknum = 50000 -> is the number of voxels that we will
% process at the
%   same time. For setups with lower memory, you may need to decrease this
%   number.

% wantmemoryoutputs is a logical vector [A B C D] indicating which of the
%     four model types to return in the output <results>. The user must be
%     careful with this, as large datasets can require a lot of RAM. If you
%     do not request the various model types, they will be cleared from
%     memory (but still potentially saved to disk). Default: [0 0 0 1]
%     which means return only the final type-D model.

% wantfileoutputs is a logical vector [A B C D] indicating which of the
%     four model types to save to disk (assuming that they are computed). A
%     = 0/1 for saving the results of the ONOFF model B = 0/1 for saving
%     the results of the FITHRF model C = 0/1 for saving the results of the
%     FITHRF_GLMdenoise model D = 0/1 for saving the results of the
%     FITHRF_GLMdenoise_RR model Default: [1 1 1 1] which means save all
%     computed results to disk.

% numpcstotry (optional) is a non-negative integer indicating the maximum
%     number of PCs to enter into the model. Default: 10.

% fracs (optional) is a vector of fractions that are greater than 0
%     and less than or equal to 1. We automatically sort in descending
%     order and ensure the fractions are unique. These fractions indicate
%     the regularization levels to evaluate using fractional ridge
%     regression (fracridge) and cross-validation. Default:
%     fliplr(.05:.05:1). A special case is when <fracs> is specified as a
%     single scalar value. In this case, cross-validation is NOT performed
%     for the type-D model, and we instead blindly use the supplied
%     fractional value for the type-D model.

% For the purpose of this example we will keep all outputs in the memory.
opt = struct('wantmemoryoutputs',[1 1 1 1]);

% This example saves output .mat files to the folder
% "example2outputs/GLMsingle". If these outputs don't already exist, we
% will perform the time-consuming call to GLMestimatesingletrial.m;
% otherwise, we will just load from disk.
if ~exist([outputdir '/GLMsingle'],'dir')
    
    [results] = GLMestimatesingletrial(design,data,stimdur,tr,[outputdir '/GLMsingle'],opt);
    
    % We assign outputs of GLMestimatesingletrial to "models" structure.
    % Note that results{1} contains GLM estimates from an ONOFF model,
    % where all images are treated as the same condition. These estimates
    % could be potentially used to find cortical areas that respond to
    % visual stimuli. We want to compare beta weights between conditions
    % therefore we are not going to store the ONOFF GLM results.
    
    clear models;
    models.FIT_HRF = results{2};
    models.FIT_HRF_GLMdenoise = results{3};
    models.FIT_HRF_GLMdenoise_RR = results{4};
    
else
    % Load existing file outputs if they exist
    results = load([outputdir '/GLMsingle/TYPEB_FITHRF.mat']);
    models.FIT_HRF = results;
    results = load([outputdir '/GLMsingle/TYPEC_FITHRF_GLMDENOISE.mat']);
    models.FIT_HRF_GLMdenoise = results;
    results = load([outputdir '/GLMsingle/TYPED_FITHRF_GLMDENOISE_RR.mat']);
    models.FIT_HRF_GLMdenoise_RR = results;
end

%% Summary of important outputs

% The outputs of GLMestimatesingletrial.m are formally documented in its
% header. Here, we highlight a few of the more important outputs:
%
% R2 -> is model accuracy expressed in terms of R^2 (percentage).
%
% modelmd -> is the full set of single-trial beta weights (X x Y x Z x
% TRIALS). Beta weights are arranged in chronological order.
%
% HRFindex -> is the 1-index of the best fit HRF. HRFs can be recovered
% with getcanonicalHRFlibrary(stimdur,tr)
%
% FRACvalue -> is the fractional ridge regression regularization level
% chosen for each voxel. Values closer to 1 mean less regularization.

%% Plot a slice of brain with GLMsingle outputs

% We are going to plot several outputs from the FIT_HRF_GLMdenoise_RR GLM,
% which contains the full set of GLMsingle optimizations.

slice_V1 = 20; % slice containing many early visual cortex voxels

% we will plot betas, R2, optimal HRF indices, and the voxel frac values
val2plot = {'modelmd';'R2';'HRFindex';'FRACvalue'};
cmaps = {cmapsign2;hot;jet;copper};

% Mask out voxels that are outside the brain
brainmask = models.FIT_HRF_GLMdenoise_RR.meanvol(:,5:end-5,slice_V1) > 250;

figure(3);clf

for v = 1 : length(val2plot)
    f=subplot(2,2,v);
    
    % Set non-brain voxels to nan to ease visualization
    plotdata = models.FIT_HRF_GLMdenoise_RR.(val2plot{v})(:,5:end-5,slice_V1);
    plotdata(~brainmask) = nan;
    
    if contains('modelmd',val2plot{v})
        imagesc(nanmean(plotdata,4),[-5 5]); axis off image;
        title('Average GLM betas (750 stimuli)')
    else
        imagesc(plotdata); axis off image;
        title(val2plot{v})
    end
    colormap(f,cmaps{v})
    colorbar
    set(gca,'FontSize',15)
end

set(gcf,'Position',[418   412   782   605])

%% Run a baseline GLM to compare with GLMsingle.

% Additionally, for comparison purposes we are going to run a standard GLM
% without HRF fitting, GLMdenoise, or ridge regression regularization. We
% will change the default settings by using the "opt" structure.

opt.wantlibrary = 0; % switch off HRF fitting
opt.wantglmdenoise = 0; % switch off GLMdenoise
opt.wantfracridge = 0; % switch off ridge regression
opt.wantfileoutputs = [0 1 0 0];
opt.wantmemoryoutputs = [0 1 0 0];

% If these outputs don't already exist, we will perform the call to
% GLMestimatesingletrial.m; otherwise, we will just load from disk.
if ~exist([outputdir '/GLMbaseline'],'dir')
    
    [ASSUME_HRF] = GLMestimatesingletrial(design,data,stimdur,tr,[outputdir '/GLMbaseline'],opt);
    models.ASSUME_HRF = ASSUME_HRF{2};
    
else
    
    % Note that even though we are loading TYPEB_FITHRF betas, HRF fitting
    % has been turned off and this struct field will thus contain the
    % outputs of a GLM fit using the canonical HRF.
    results = load([outputdir '/GLMbaseline/TYPEB_FITHRF.mat']);
    models.ASSUME_HRF = results;
    
end

% We assign outputs from GLMestimatesingletrial to "models" structure.
% Again, results{1} contains GLM estimates from an ONOFF model so we are
% not going to extract it.

%%

% Now, "models" variable holds solutions for 4 GLM models
disp(fieldnames(models))

%% Organize GLM outputs to enable calculation of voxel reliability

% To compare the results of different GLMs we are going to calculate the
% voxel-wise split-half reliablity for each model. Reliability values
% reflect a correlation between beta weights for repeated presentations of
% the same conditions. In short, we are going to check how
% reliable/reproducible are the single trial responses to repeated
% conditions estimated with each GLM type.

% This NSD floc scan session has 6 repetitions of each condition per run.
% In the code below, we are attempting to locate the indices in the beta
% weight GLMsingle outputs modelmd(x,y,z,trials) that correspond to
% repeated conditions.

% Consolidate design matrices
designALL = cat(1,design{:});

% Construct a vector containing 1-indexed condition numbers in
% chronological order.

corder = [];
for p=1:size(designALL,1)
    if any(designALL(p,:))
        corder = [corder find(designALL(p,:))];
    end
end

%%

% let's take a look at the first few entries
corder(1:3)

% Note that [10 8 7] means that the first stimulus block involved
% presentation of the 10th condition, the second stimulus block involved
% presentation of the 8th condition, and so on.

%%

% In order to compute split-half reliability, we have to do some indexing.
% We want to find all repetitions of the same condition. For example we can
% look up when during the 4 blocks image 1 was repeated. Each condition
% should be repeated exactly 24 times.

fprintf('Condition 1 was repeated %i times across 4 runs, with GLMsingle betas at the following indices:\n',length(find(corder==1)));
find(corder==1)

% Now, for each voxel we are going to correlate beta weights describing the
% response to averaged even presentations of the 10 conditions with beta
% weights describing the responses from the averaged odd repetitions of the
% same conditions, over time. With 10 conditions in the localizer set, the
% split-half correlation for each voxel will thus reflect the relationship
% between two vectors with 10 beta weights each.

%% Compute median split-half reliability for each GLM version

% To calculate the split-half reliability we are going to average the odd
% and even beta weights extracted from the same condition and calculate the
% correlation coefficent between these values. We do this for each voxel
% inside the primary visual cortex and face-selective cortical ROIs.

% We first arrange models from least to most sophisticated (for
% visualization purposes)
model_names = fieldnames(models);
model_names = model_names([4 1 2 3]);

% Create output variable for reliability values
vox_reliabilities = cell(1,length(models));

% For each GLM...
for m = 1 : length(model_names)
    
    % Get the GLM betas
    modelmd = models.(model_names{m}).modelmd;
    
    dims = size(modelmd);
    Xdim = dims(1);
    Ydim = dims(2);
    Zdim = dims(3);
    
    cond = size(design{1},2);
    reps = dims(4)/cond;
    
    % Create an empty variable for storing betas grouped together by
    % condition (X, Y, Z, nReps, nConditions)
    betas = nan(Xdim,Ydim,Zdim,reps,cond);
    
    % Populate repetition beta variable by iterating through conditions
    for c = 1 : length(unique(corder))
        
        indx = find(corder == c);
        betas(:,:,:,:,c) = modelmd(:,:,:,indx);
        
    end

    % Output variable for reliability values
    vox_reliability = NaN(Xdim, Ydim, Zdim);

    % Loop through voxels in the fMRI volume
    for i = 1:Xdim
        for j = 1:Ydim
            for k = 1:Zdim
                
                % Calculate the reliability only for voxels within the
                % EarlyVis and FFA ROIs, for the sake of efficiency
                if visual.ROI(i,j,k) > 0 || floc.ROI(i,j,k) > 0
                    
                    vox_data  = squeeze(betas(i,j,k,:,:));
                    even_data = nanmean(vox_data(1:2:end,:));
                    odd_data  = nanmean(vox_data(2:2:end,:));
                    
                    % Reliability is the split-half correlation between odd
                    % and even presentations
                    vox_reliability(i,j,k) = corr(even_data', odd_data');
                    
                end
            end
        end
    end
    
    % Store reliablity for each model
    vox_reliabilities{m} = vox_reliability;
    
end

%% Compare visual voxel reliabilities between beta versions in V1 and FFA ROIs

figure(5);clf

% For each GLM type we will calculate median reliability for voxels within
% the visual ROIs and draw a bar plot for FFA and V1.

slice_V1 = 10; % good slice for viewing V1
slice_ffa = 3; % good slice for viewing FFA

for s = 1 : 5
    
    subplot(2,5,s)
    underlay = data{1}(:,:,slice_V1,1);
    overlay  = visual.ROI(:,:,slice_V1)==1; % index 1 corresponds to V1 in this mask variable
    underlay_im = cmaplookup(underlay,min(underlay(:)),max(underlay(:)),[],gray(256));
    overlay_im = cmaplookup(overlay,-0.5,0.5,[],[0 0 1]);
    mask = visual.ROI(:,:,slice_V1)==1;
    
    hold on
    imagesc(imrotate(underlay_im,180));
    imagesc(imrotate(overlay_im,180), 'AlphaData', imrotate(mask,180));
    title(sprintf('V1 voxels, slice = %i',slice_V1))
    slice_V1 = slice_V1 + 1;
    axis image
    axis off
    set(gca,'FontSize',14)
    
    subplot(2,5,s+5)
    underlay = data{1}(:,:,slice_ffa,1);
    overlay  = floc.ROI(:,:,slice_ffa)==2; % index 2 corresponds to FFA in this mask variable
    underlay_im = cmaplookup(underlay,min(underlay(:)),max(underlay(:)),[],gray(256));
    overlay_im = cmaplookup(overlay,-0.5,0.5,[],round([237 102 31]/255,2));
    mask = floc.ROI(:,:,slice_ffa)==2;
    
    hold on
    imagesc(imrotate(underlay_im,180));
    imagesc(imrotate(overlay_im,180), 'AlphaData', imrotate(mask,180));
    title(sprintf('FFA voxels, slice = %i',slice_ffa))
    slice_ffa = slice_ffa + 1;
    axis image
    axis off
    set(gca,'FontSize',14)
    
end

set(gcf,'Position',[218   212   1400   500])

%% Summary: median ROI voxel reliability for each beta version

% To perform a comparison between all GLM versions available in our visual
% ROIs, we will simply loop through the beta versions, isolate the voxels
% of interest, compute the median split-half correlation value within each
% ROI, and plot using a bar graph.

figure(6)

cmap = [0.2314    0.6039    0.6980
    0.8615    0.7890    0.2457
    0.8824    0.6863         0
    0.9490    0.1020         0];

% For each GLM type we calculate median reliability for voxels within V1
% and FFA
plot_data = zeros(length(vox_reliabilities),2);
for m = 1 : 4
    
    vox_reliability = vox_reliabilities{m};
    
    % floc.ROI==2 points to FFA voxels, visual.ROI==1 points to V1 voxels
    plot_data(m,:) = [nanmedian(vox_reliability(floc.ROI==2)) nanmedian(vox_reliability(visual.ROI==1))];
    
end

bar(plot_data)
ylabel('Median reliability')
set(gca,'Fontsize',12)
set(gca,'TickLabelInterpreter','none')
xtickangle(0)
legend({'FFA';'V1'},'Interpreter','None','Location','NorthWest')
set(gcf,'Position',[418   412   1000   605])
title('Median voxel split-half reliability of GLM models')
xticklabels(model_names')
set(gca,'FontSize',14)

% Notice that there is systematic increase in reliability moving from the
% first to the second to the third to the final fourth version of the GLM
% results, within both the early visual and face-selective ROIs. These
% increases reflect, respectively, the addition of HRF fitting, the
% derivation and use of data-driven nuisance regressors, and the use of
% ridge regression as a way to regularize the instability of closely spaced
% experimental trials. Depending on one's experimental goals, it is
% possible with setting of option flags to activate a subset of these
% analysis features.
% 
% Also, keep in mind that in the above figure, we are simply showing the
% median as a metric of the central tendency (you may want to peruse
% individual voxels in scatter plots, for example).
% 
% Note also that median reliability is consistently higher in the FFA ROI
% than in the V1 ROI in this analysis of localizer data. Importantly, this
% does not imply that V1 data are inherently noisy or unreliable across the
% NSD dataset (e.g. in the NSD core experiment). Here we are analyzing GLM
% outputs from the localizer dataset, which was designed to identify neural
% ROIs whose voxel tuning profiles vary meaningfully across a small,
% curated set of high-level stimulus categories. As such, we would expect
% the FFA voxels to have relatively more well-defined tuning curves, with
% more substantial variability in GLM betas between the localizer
% categories, than the V1 voxels, whose tuning is determined by lower-level
% image features that do not necessarily vary in systematic ways between
% the localizer categories. For these reasons, one might expect that
% split-half correlations would be somewhat lower in the V1 voxels using
% our particular metric of reliability (odd-even correlation) in this
% dataset. Different metrics of reliability may generate different patterns
% of results when comparing these two ROIs, and we would also expect
% results to vary depending on the stimulus set.

%%

% We now plot the improvement of reliability when comparing
% FIT_HRF_GLMDENOISE_RR with ASSUME_HRF, with higher positive values
% reflecting greater benefit from applying GLMsingle.

figure(7)

% Comparison is the final output (FIT_HRF_GLMDENOISE_RR) vs. the baseline
% GLM (ASSUME_HRF)
vox_improvement = vox_reliabilities{4} - vox_reliabilities{1};

slice = 4;

ROI = visual.ROI == 1 | floc.ROI == 2;

for s = 1:15
    
    subplot(3,5,s) 
    underlay = data{1}(:,5:end-5,slice,1);
    overlay  = vox_improvement(:,5:end-5,slice);
    underlay_im = cmaplookup(underlay,min(underlay(:)),max(underlay(:)),[],gray(256));
    overlay_im = cmaplookup(overlay,-0.3,0.3,[],cmapsign2);
    mask = ROI(:,5:end-5,slice)==1;
    hold on
    imagesc(imrotate(underlay_im,180));
    imagesc(imrotate(overlay_im,180), 'AlphaData', imrotate(mask,180));
    title(['median ROI \Delta{\itr} in slice:' newline '+' num2str(nanmedian(overlay(:)),3)])
    slice = slice + 1;
    axis image
    colormap(cmapsign2)
    c = colorbar;
    c.TickLabels = {'-0.3';'0';'0.3'};
    xticks([])
    yticks([])
    set(gca,'FontSize',14)
end

set(gcf,'Position',[100   100   1600   700])
    


##### SOURCE END #####
--></body></html>