"""
Accuracy validation utilities for GLMsingle PyTorch.

This module provides comprehensive numerical accuracy validation to ensure
that the PyTorch implementation produces identical results to the original
NumPy implementation within numerical precision limits.

Key Features:
- Comprehensive numerical accuracy testing
- Statistical significance testing for differences
- Tolerance-based validation with configurable thresholds
- Detailed error analysis and reporting
- Regression testing capabilities

Validation Metrics:
- Absolute and relative error measurements
- Statistical tests for numerical equivalence
- Correlation analysis between implementations
- Distribution comparison and outlier detection
- Comprehensive error reporting and visualization
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
import matplotlib.pyplot as plt
from scipy import stats
import warnings

from ..config import GLMSingleConfig
from ..backends.torch_backend import TorchBackend
from ..torch_ops.linear_algebra import TorchLinearAlgebra
from ..torch_ops.convolution import TorchConvolution
from ..torch_ops.statistics import TorchStatistics


@dataclass
class ValidationResult:
    """Container for validation results."""
    operation_name: str
    max_abs_error: float
    mean_abs_error: float
    max_rel_error: float
    mean_rel_error: float
    correlation: float
    passed: bool
    tolerance_abs: float
    tolerance_rel: float
    additional_metrics: Optional[Dict] = None


class AccuracyValidator:
    """
    Comprehensive accuracy validation for GLMsingle PyTorch.
    
    This class provides detailed numerical accuracy validation to ensure
    that the PyTorch implementation produces results that are numerically
    equivalent to the original NumPy implementation.
    
    The validator uses multiple metrics and statistical tests to validate
    accuracy across all major operations and provides detailed error
    analysis for any discrepancies found.
    """
    
    def __init__(self, config: Optional[GLMSingleConfig] = None,
                 tolerance_abs: float = 1e-5,
                 tolerance_rel: float = 1e-4):
        """
        Initialize accuracy validator.
        
        Args:
            config: Configuration for PyTorch backend
            tolerance_abs: Absolute error tolerance
            tolerance_rel: Relative error tolerance
        """
        self.config = config or GLMSingleConfig()
        self.backend = TorchBackend(self.config)
        
        # Initialize operation modules
        self.linalg = TorchLinearAlgebra(self.backend)
        self.conv = TorchConvolution(self.backend)
        self.stats = TorchStatistics(self.backend)
        
        # Validation parameters
        self.tolerance_abs = tolerance_abs
        self.tolerance_rel = tolerance_rel
        
        # Results storage
        self.results: List[ValidationResult] = []
        
        print(f"Accuracy validator initialized:")
        print(f"  Device: {self.backend.device}")
        print(f"  Absolute tolerance: {tolerance_abs}")
        print(f"  Relative tolerance: {tolerance_rel}")
    
    def validate_linear_algebra(self, test_sizes: List[Tuple[int, int]] = None) -> List[ValidationResult]:
        """
        Validate linear algebra operations accuracy.
        
        Args:
            test_sizes: List of matrix sizes to test
            
        Returns:
            List of validation results
        """
        if test_sizes is None:
            test_sizes = [(100, 50), (500, 100), (1000, 200)]
        
        print("Validating linear algebra operations...")
        results = []
        
        for n_samples, n_features in test_sizes:
            print(f"  Testing size: {n_samples} x {n_features}")
            
            # Generate test data
            np.random.seed(42)  # For reproducibility
            X_np = np.random.randn(n_samples, n_features).astype(np.float32)
            y_np = np.random.randn(n_samples, 50).astype(np.float32)
            
            # Test OLS matrix computation
            torch_result = self.backend.to_numpy(self.linalg.olsmatrix(X_np))
            numpy_result = self._numpy_olsmatrix(X_np)
            
            result = self._validate_arrays(
                "OLS Matrix", torch_result, numpy_result
            )
            results.append(result)
            
            # Test ridge regression
            alphas = np.array([0.1, 1.0, 10.0])
            torch_result = self.backend.to_numpy(
                self.linalg.ridge_regression(X_np, y_np, alphas)
            )
            numpy_result = self._numpy_ridge_regression(X_np, y_np, alphas)
            
            result = self._validate_arrays(
                "Ridge Regression", torch_result, numpy_result
            )
            results.append(result)
        
        self.results.extend(results)
        return results
    
    def validate_convolution(self, test_sizes: List[Tuple[int, int]] = None) -> List[ValidationResult]:
        """
        Validate convolution operations accuracy.
        
        Args:
            test_sizes: List of sizes to test
            
        Returns:
            List of validation results
        """
        if test_sizes is None:
            test_sizes = [(200, 20), (500, 50), (1000, 100)]
        
        print("Validating convolution operations...")
        results = []
        
        # Generate HRF
        np.random.seed(42)
        hrf_np = np.random.randn(20).astype(np.float32)
        
        for n_timepoints, n_conditions in test_sizes:
            print(f"  Testing size: {n_timepoints} x {n_conditions}")
            
            # Generate test design matrix
            X_np = np.random.randn(n_timepoints, n_conditions).astype(np.float32)
            
            # Test convolution
            torch_result = self.backend.to_numpy(
                self.conv.convolve_design(X_np, hrf_np, mode='same')
            )
            numpy_result = self._numpy_convolve_design(X_np, hrf_np)
            
            result = self._validate_arrays(
                "HRF Convolution", torch_result, numpy_result
            )
            results.append(result)
        
        self.results.extend(results)
        return results
    
    def validate_statistics(self, test_sizes: List[Tuple[int, int]] = None) -> List[ValidationResult]:
        """
        Validate statistical operations accuracy.
        
        Args:
            test_sizes: List of sizes to test
            
        Returns:
            List of validation results
        """
        if test_sizes is None:
            test_sizes = [(200, 100), (500, 500), (1000, 1000)]
        
        print("Validating statistical operations...")
        results = []
        
        for n_timepoints, n_voxels in test_sizes:
            print(f"  Testing size: {n_timepoints} x {n_voxels}")
            
            # Generate test data
            np.random.seed(42)
            x_np = np.random.randn(n_timepoints, n_voxels).astype(np.float32)
            y_np = x_np + 0.1 * np.random.randn(n_timepoints, n_voxels).astype(np.float32)
            
            # Test R² calculation
            torch_result = self.backend.to_numpy(
                self.stats.calc_cod(x_np, y_np)
            )
            numpy_result = self._numpy_calc_cod(x_np, y_np)
            
            result = self._validate_arrays(
                "R² Calculation", torch_result, numpy_result
            )
            results.append(result)
            
            # Test correlation calculation
            torch_result = self.backend.to_numpy(
                self.stats.calculate_correlation(x_np, y_np, dim=0)
            )
            numpy_result = self._numpy_correlation(x_np, y_np)
            
            result = self._validate_arrays(
                "Correlation", torch_result, numpy_result
            )
            results.append(result)
        
        self.results.extend(results)
        return results
    
    def validate_edge_cases(self) -> List[ValidationResult]:
        """
        Validate handling of edge cases and special values.
        
        Returns:
            List of validation results
        """
        print("Validating edge cases...")
        results = []
        
        # Test with zeros
        X_zeros = np.zeros((100, 50), dtype=np.float32)
        torch_result = self.backend.to_numpy(self.linalg.olsmatrix(X_zeros))
        numpy_result = self._numpy_olsmatrix(X_zeros)
        
        result = self._validate_arrays(
            "OLS Matrix (zeros)", torch_result, numpy_result
        )
        results.append(result)
        
        # Test with NaN values
        X_nan = np.random.randn(100, 50).astype(np.float32)
        X_nan[10:20, 5:10] = np.nan
        y_nan = np.random.randn(100, 20).astype(np.float32)
        y_nan[15:25, 2:8] = np.nan
        
        torch_result = self.backend.to_numpy(
            self.stats.calc_cod(X_nan, y_nan, want_safe=1)
        )
        numpy_result = self._numpy_calc_cod_safe(X_nan, y_nan)
        
        result = self._validate_arrays(
            "R² with NaN", torch_result, numpy_result
        )
        results.append(result)
        
        # Test with very small values
        X_small = np.random.randn(100, 50).astype(np.float32) * 1e-10
        torch_result = self.backend.to_numpy(self.linalg.olsmatrix(X_small))
        numpy_result = self._numpy_olsmatrix(X_small)
        
        result = self._validate_arrays(
            "OLS Matrix (small values)", torch_result, numpy_result,
            tolerance_abs=1e-15, tolerance_rel=1e-10
        )
        results.append(result)
        
        self.results.extend(results)
        return results
    
    def _validate_arrays(self, operation_name: str,
                        torch_result: np.ndarray,
                        numpy_result: np.ndarray,
                        tolerance_abs: Optional[float] = None,
                        tolerance_rel: Optional[float] = None) -> ValidationResult:
        """
        Validate two arrays for numerical equivalence.
        
        Args:
            operation_name: Name of the operation being validated
            torch_result: Result from PyTorch implementation
            numpy_result: Result from NumPy implementation
            tolerance_abs: Absolute tolerance (uses default if None)
            tolerance_rel: Relative tolerance (uses default if None)
            
        Returns:
            ValidationResult object
        """
        if tolerance_abs is None:
            tolerance_abs = self.tolerance_abs
        if tolerance_rel is None:
            tolerance_rel = self.tolerance_rel
        
        # Handle shape mismatches
        if torch_result.shape != numpy_result.shape:
            print(f"Warning: Shape mismatch for {operation_name}")
            print(f"  PyTorch: {torch_result.shape}, NumPy: {numpy_result.shape}")
            return ValidationResult(
                operation_name=operation_name,
                max_abs_error=float('inf'),
                mean_abs_error=float('inf'),
                max_rel_error=float('inf'),
                mean_rel_error=float('inf'),
                correlation=0.0,
                passed=False,
                tolerance_abs=tolerance_abs,
                tolerance_rel=tolerance_rel
            )
        
        # Handle NaN values
        torch_finite = np.isfinite(torch_result)
        numpy_finite = np.isfinite(numpy_result)
        
        # Check if NaN patterns match
        nan_mismatch = np.any(torch_finite != numpy_finite)
        
        # Calculate errors only for finite values
        finite_mask = torch_finite & numpy_finite
        
        if not np.any(finite_mask):
            # All values are NaN or infinite
            correlation = 1.0 if np.array_equal(torch_finite, numpy_finite) else 0.0
            max_abs_error = 0.0 if not nan_mismatch else float('inf')
            mean_abs_error = 0.0 if not nan_mismatch else float('inf')
            max_rel_error = 0.0 if not nan_mismatch else float('inf')
            mean_rel_error = 0.0 if not nan_mismatch else float('inf')
        else:
            torch_finite_vals = torch_result[finite_mask]
            numpy_finite_vals = numpy_result[finite_mask]
            
            # Calculate absolute errors
            abs_errors = np.abs(torch_finite_vals - numpy_finite_vals)
            max_abs_error = np.max(abs_errors)
            mean_abs_error = np.mean(abs_errors)
            
            # Calculate relative errors
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                rel_errors = abs_errors / (np.abs(numpy_finite_vals) + 1e-15)
                max_rel_error = np.max(rel_errors)
                mean_rel_error = np.mean(rel_errors)
            
            # Calculate correlation
            if len(torch_finite_vals) > 1:
                correlation = np.corrcoef(torch_finite_vals, numpy_finite_vals)[0, 1]
                if np.isnan(correlation):
                    correlation = 1.0 if np.allclose(torch_finite_vals, numpy_finite_vals) else 0.0
            else:
                correlation = 1.0
        
        # Determine if validation passed
        passed = (
            max_abs_error <= tolerance_abs and
            max_rel_error <= tolerance_rel and
            not nan_mismatch and
            correlation > 0.99
        )
        
        # Additional metrics
        additional_metrics = {
            'nan_mismatch': nan_mismatch,
            'finite_values': np.sum(finite_mask),
            'total_values': torch_result.size
        }
        
        return ValidationResult(
            operation_name=operation_name,
            max_abs_error=max_abs_error,
            mean_abs_error=mean_abs_error,
            max_rel_error=max_rel_error,
            mean_rel_error=mean_rel_error,
            correlation=correlation,
            passed=passed,
            tolerance_abs=tolerance_abs,
            tolerance_rel=tolerance_rel,
            additional_metrics=additional_metrics
        )
    
    # NumPy reference implementations
    def _numpy_olsmatrix(self, X: np.ndarray) -> np.ndarray:
        """NumPy reference implementation."""
        from sklearn.preprocessing import normalize
        
        good = ~np.all(X == 0, axis=0)
        f = np.zeros((X.shape[1], X.shape[0]))
        
        if np.any(good):
            X_good = X[:, good]
            X_norm = normalize(X_good, axis=0)
            norms = np.linalg.norm(X_good, axis=0)
            
            XtX = X_norm.T @ X_norm
            XtX_inv = np.linalg.pinv(XtX)
            temp = np.diag(1.0 / norms) @ XtX_inv @ X_norm.T
            f[good, :] = temp
        
        return f
    
    def _numpy_ridge_regression(self, X: np.ndarray, y: np.ndarray, 
                               alphas: np.ndarray) -> np.ndarray:
        """NumPy reference implementation."""
        XtX = X.T @ X
        Xty = X.T @ y
        
        results = []
        for alpha in alphas:
            XtX_reg = XtX + alpha * np.eye(XtX.shape[0])
            try:
                coeffs = np.linalg.solve(XtX_reg, Xty)
            except np.linalg.LinAlgError:
                coeffs = np.linalg.lstsq(XtX_reg, Xty, rcond=None)[0]
            results.append(coeffs)
        
        return np.array(results)
    
    def _numpy_convolve_design(self, X: np.ndarray, hrf: np.ndarray) -> np.ndarray:
        """NumPy reference implementation."""
        n_time, n_cond = X.shape
        result = np.zeros_like(X)
        
        for i in range(n_cond):
            conv_result = np.convolve(X[:, i], hrf, mode='same')
            result[:, i] = conv_result
        
        return result
    
    def _numpy_calc_cod(self, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """NumPy reference implementation."""
        ss_res = np.sum((y - x) ** 2, axis=0)
        ss_tot = np.sum(y ** 2, axis=0)
        
        with np.errstate(divide='ignore', invalid='ignore'):
            r2 = 100 * (1 - ss_res / ss_tot)
            r2[ss_tot == 0] = np.nan
        
        return r2
    
    def _numpy_calc_cod_safe(self, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """NumPy reference implementation with NaN handling."""
        mask = np.isnan(x) | np.isnan(y)
        x_safe = x.copy()
        y_safe = y.copy()
        x_safe[mask] = np.nan
        y_safe[mask] = np.nan
        
        ss_res = np.nansum((y_safe - x_safe) ** 2, axis=0)
        ss_tot = np.nansum(y_safe ** 2, axis=0)
        
        with np.errstate(divide='ignore', invalid='ignore'):
            r2 = 100 * (1 - ss_res / ss_tot)
            r2[ss_tot == 0] = np.nan
        
        return r2
    
    def _numpy_correlation(self, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """NumPy reference implementation."""
        x_centered = x - np.mean(x, axis=0)
        y_centered = y - np.mean(y, axis=0)
        
        numerator = np.sum(x_centered * y_centered, axis=0)
        x_var = np.sum(x_centered ** 2, axis=0)
        y_var = np.sum(y_centered ** 2, axis=0)
        denominator = np.sqrt(x_var * y_var)
        
        with np.errstate(divide='ignore', invalid='ignore'):
            corr = numerator / denominator
            corr[denominator == 0] = np.nan
        
        return corr
    
    def generate_report(self, save_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate comprehensive validation report.
        
        Args:
            save_path: Path to save report (optional)
            
        Returns:
            Dict containing validation report
        """
        if not self.results:
            print("No validation results available. Run validation first.")
            return {}
        
        # Calculate summary statistics
        passed_tests = [r for r in self.results if r.passed]
        failed_tests = [r for r in self.results if not r.passed]
        
        report = {
            'summary': {
                'total_tests': len(self.results),
                'passed_tests': len(passed_tests),
                'failed_tests': len(failed_tests),
                'pass_rate': len(passed_tests) / len(self.results) * 100
            },
            'tolerances': {
                'absolute': self.tolerance_abs,
                'relative': self.tolerance_rel
            },
            'results': []
        }
        
        # Add detailed results
        for result in self.results:
            report['results'].append({
                'operation': result.operation_name,
                'passed': result.passed,
                'max_abs_error': result.max_abs_error,
                'mean_abs_error': result.mean_abs_error,
                'max_rel_error': result.max_rel_error,
                'mean_rel_error': result.mean_rel_error,
                'correlation': result.correlation
            })
        
        # Add failure analysis
        if failed_tests:
            report['failures'] = []
            for result in failed_tests:
                report['failures'].append({
                    'operation': result.operation_name,
                    'max_abs_error': result.max_abs_error,
                    'max_rel_error': result.max_rel_error,
                    'correlation': result.correlation
                })
        
        # Save report if path provided
        if save_path:
            import json
            with open(save_path, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            print(f"Validation report saved to {save_path}")
        
        return report
